rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is admin via custom claims
    function isAdmin() {
      return request.auth != null && 
             request.auth.token.admin == true;
    }
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the document
    function isOwner(userId) {
      return request.auth != null && 
             request.auth.uid == userId;
    }
    
    // Users collection - strict access control
    match /users/{userId} {
      // Users can only get their own profile, admins can get any profile.
      allow get: if isOwner(userId) || isAdmin();
      // Admins can list all profiles
      allow list: if isAdmin();
      
      // Users can only create their own profile during registration
      allow create: if isAuthenticated() && 
                   isOwner(userId) &&
                   // Only allow doctor or assistant roles in Firestore
                   (request.resource.data.role == 'doctor' || 
                    request.resource.data.role == 'assistant');
      
      // Users can update their own profile, but not their role or email
      allow update: if isOwner(userId)
                   // keep email immutable
                  && request.resource.data.email == resource.data.email
                  // allow changing role but only within the safe set
                  && (request.resource.data.role in ['doctor', 'assistant']);
      
      // Only admins can delete users or update roles/other users' profiles
      allow delete, update: if isAdmin();
    }
    
    // Pending users collection - for registration workflow
    match /pendingUsers/{pendingUserId} {
      // Anyone can create a pending user (public registration)
      allow create: if true;
      
      // Only admins can read, update, or delete pending users
      allow read, update, delete: if isAdmin();
    }
    
    // Patients collection
    match /patients/{patientId} {
      // Doctors can create patients and must be the owner
      allow create: if isOwner(request.resource.data.doctorId);
      
      // Doctors can read, update, and delete their own patients. Admins have full access.
      allow read, update, delete: if isOwner(resource.data.doctorId) || isAdmin();
    }

    match /users/{userId}/istem-formu/{formId} {
      allow create: if isOwner(userId);
      allow read, delete: if isOwner(userId) || isAdmin();
      // Only allow updates if user owns the document OR if admin is completing the form
      allow update: if isOwner(userId) ||
                    (isAdmin() &&
                     request.resource.data.status == 'completed' &&
                     resource.data.status == 'pending');
    }

    // Patient images collection - for standalone images not associated with istem forms
    match /patient-images/{imageId} {
      // Only admins can create patient images
      allow create: if isAdmin();

      // Users can read images for their own patients, admins can read all
      allow read: if isAuthenticated() &&
                  (isAdmin() ||
                   // Check if the patient belongs to the requesting user
                   exists(/databases/$(database)/documents/patients/$(resource.data.patientId)) &&
                   get(/databases/$(database)/documents/patients/$(resource.data.patientId)).data.doctorId == request.auth.uid);

      // Only admins can delete patient images
      allow delete: if isAdmin();

      // Only admins can update patient images (for report management)
      allow update: if isAdmin();
    }
    
    // Future collections can be added here with appropriate rules
    // For example, if you add patients, appointments, etc.
  }
} 