# Security Migration Guide

## Step 1: Environment Variables

### 1.1 Set JWT Secret for Cloud Functions

In your functions directory, ensure you have a strong JWT secret:

```bash
cd functions
firebase functions:config:set jwt.secret="your-very-strong-secret-key-here"
```

**Generate a secure secret:**

```bash
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### 1.2 Verify Environment Config

```bash
firebase functions:config:get
```

## Step 2: Deploy Cloud Functions

Deploy the updated functions with security improvements:

```bash
cd functions
npm run build
firebase deploy --only functions
```

**Functions deployed:**

- `sendCustomEmail` - Now uses custom claims
- `generateRegistrationToken` - Now uses custom claims  
- `setAdminClaim` - New function to manage admin privileges

## Step 3: Deploy Firestore Rules

Deploy the new security rules:

```bash
firebase deploy --only firestore:rules
```

**⚠️ Important:** This will immediately restrict access. Ensure Step 4 is completed promptly.

## Step 4: Set Admin Custom Claims

### 4.1 Install Firebase Admin SDK

In your project root:

```bash
npm install firebase-admin
```

### 4.2 Set Up Service Account Credentials

1. Go to Firebase Console > Project Settings > Service Accounts
2. Click "Generate new private key" and download the JSON file
3. Set the environment variable:

**Windows PowerShell:**

```powershell
$env:GOOGLE_APPLICATION_CREDENTIALS="path\to\your\service-account-key.json"
```

**Windows Command Prompt:**

```cmd
set GOOGLE_APPLICATION_CREDENTIALS=path\to\your\service-account-key.json
```

**Linux/Mac:**

```bash
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

### 4.3 Configure Admin Emails

Edit `scripts/set-admin-claims.js` and update the `ADMIN_EMAILS` array:

```javascript
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
];
```

### 4.4 Run Admin Claims Script

```bash
node scripts/set-admin-claims.js
```

This will:

- Set admin custom claims for specified users
- Show a summary of all users and their admin status
- Provide clear feedback on success/failure

## Step 5: Update Admin Dashboard Access Control

### 5.1 Create Admin Route Protection

Update your admin pages to use the new `isAdmin` hook:

```typescript
// In app/dashboard/admin/page.tsx or middleware
import { useUser } from '@/lib/hooks/useUser';

export default function AdminPage() {
  const { isAdmin, loading } = useUser();
  
  if (loading) return <Loading />;
  if (!isAdmin) return <div>Access Denied</div>;
  
  // Admin content...
}
```

### 5.2 Add Server-Side Protection (Recommended)

Create middleware to protect admin routes:

```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  if (request.nextUrl.pathname.startsWith('/dashboard/admin')) {
    // Verify admin claims server-side
    const token = request.cookies.get('__session')?.value;
    
    if (!token || !await verifyAdminToken(token)) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }
}
```

## Step 6: Testing and Validation

### 6.1 Test User Registration Flow

1. Submit signup form (should validate with Zod)
2. Admin approves user
3. User receives email with JWT token
4. User completes registration (should validate password strength)

### 6.2 Test Admin Functions

1. Admin can approve/reject pending users
2. Admin can generate registration tokens
3. Admin can send custom emails
4. Non-admin users cannot access these functions

### 6.3 Test Security Rules

1. Users can only read/write their own profiles
2. Users cannot escalate their role
3. Pending users can be created by anyone
4. Only admins can manage pending users

## Step 7: Monitoring and Security

### 7.1 Enable Logging

Monitor Cloud Functions logs for security events:

```bash
firebase functions:log
```
