# Brevo Email Integration Setup Guide

This guide will help you set up Brevo (formerly Sendinblue) email integration for your Dento application.

## Prerequisites

1. A Brevo account (sign up at https://www.brevo.com)
2. Firebase project with Functions enabled
3. Node.js and npm installed

## Step 1: Get Brevo API Key

1. Login to your Brevo account
2. Go to **Account Settings** → **SMTP & API**
3. Click on **Generate a new API key**
4. Copy the API key (it starts with `xkeysib-`)

## Step 2: Configure Environment Variables

Create a `.env` file in the `functions/` directory:

```bash
# Brevo Email Configuration
BREVO_API_KEY=xkeysib-your-api-key-here
BREVO_FROM_EMAIL=<EMAIL>
BREVO_FROM_NAME=Dento
```

**Important:** Add `.env` to your `.gitignore` file to avoid committing sensitive data.

## Step 3: Environment Configuration

The system automatically detects the environment and uses the correct URLs:

- **Development**: `http://localhost:3000` (when running `next dev`)
- **Production**: `https://dento-psi.vercel.app` (when deployed)

The URL detection is handled automatically in `functions/src/utils/urlUtils.ts`. If you need to change the production URL, update it there:

```typescript
return isDevelopment 
  ? 'http://localhost:3000' 
  : 'https://your-production-domain.com';
```

## Step 4: Deploy Firebase Functions

```bash
cd functions
npm run build
firebase deploy --only functions
```

## Step 5: Test Email Functionality

### Test Password Reset
1. Go to `/auth/forgot-password`
2. Enter an email address
3. Submit the form
4. Check the email inbox

### Test Registration Approval
1. As an admin, approve a pending user
2. The user should receive a registration email
3. When they complete registration, they should receive a welcome email

## Email Templates

The system includes three pre-built email templates:

### 1. Registration Approval Email
- **Trigger:** When a pending user is approved
- **Content:** Welcome message with registration link
- **Expiry:** 48 hours

### 2. Password Reset Email  
- **Trigger:** When user requests password reset
- **Content:** Password reset link with security warnings
- **Expiry:** 1 hour

### 3. Welcome Email
- **Trigger:** When a new user account is created
- **Content:** Welcome message with platform features

## Custom Email Templates

To customize email templates, edit the methods in `functions/src/services/brevoService.ts`:

- `getRegistrationApprovalTemplate()`
- `getPasswordResetTemplate()`
- `getWelcomeTemplate()`

## Client-Side Usage

Use the email service from your frontend:

```typescript
import { emailService } from '@/lib/services/emailService';

// Send password reset email
await emailService.sendPasswordResetEmail('<EMAIL>');

// Send custom email (admin only)
await emailService.sendCustomEmail({
  to: '<EMAIL>',
  subject: 'Custom Subject',
  htmlContent: '<h1>Custom HTML content</h1>'
});
```

## Troubleshooting

### Common Issues

1. **"API key is invalid"**
   - Verify your API key is correct
   - Check the key hasn't expired
   - Ensure the key has email sending permissions

2. **"From email not verified"**
   - Add your domain to Brevo
   - Verify your sender email address
   - Use a verified domain in `BREVO_FROM_EMAIL`

3. **"Function timeout"**
   - Check Firebase Functions logs
   - Verify network connectivity
   - Check Brevo service status

### Logs and Monitoring

Check Firebase Functions logs:
```bash
firebase functions:log
```

Monitor email delivery in Brevo dashboard:
- Go to **Campaigns** → **Transactional**
- View delivery statistics and logs

## Security Considerations

1. **Environment Variables**: Never commit API keys to version control
2. **Rate Limiting**: Brevo has sending limits based on your plan
3. **Spam Prevention**: Use verified domains and proper email content
4. **User Privacy**: Only send emails to consented users

## Brevo Pricing

- **Free Plan**: 300 emails/day
- **Lite Plan**: Starting from €25/month
- **Premium Plan**: Advanced features and higher limits

Visit https://www.brevo.com/pricing/ for current pricing.

## Support

- **Brevo Documentation**: https://developers.brevo.com/
- **Firebase Functions**: https://firebase.google.com/docs/functions
- **Project Issues**: Create an issue in the project repository 