'use client';

import { useState, useEffect } from 'react';
import { User, Mail, Calendar, CheckCircle, XCircle, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { PendingUser } from '@/lib/types';
import { pendingUserService } from '@/lib/services/pendingUserService';
import { formatDateTime } from '@/lib/utils';
import { useDentoConfirm } from '@/lib/hooks/useDentoConfirm';

// Helper function to get initials
const getInitials = (firstName: string, lastName: string) => {
  return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
};

export default function PendingUsersTable() {
  const [pendingUsers, setPendingUsers] = useState<PendingUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());
  const { confirm, ConfirmModal } = useDentoConfirm();

  // Fetch pending users
  const fetchPendingUsers = async () => {
    try {
      setLoading(true);
      const users = await pendingUserService.getByStatus('pending');
      const sortedUsers = users.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      setPendingUsers(sortedUsers);
    } catch (error) {
      console.error('Error fetching pending users:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingUsers();
  }, []);

  // Approve user
  const handleApproveUser = async (pendingUser: PendingUser) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Onayla',
      message: `${pendingUser.firstName} ${pendingUser.lastName} kullanıcısını onaylamak istediğinizden emin misiniz? Onaylandıktan sonra kullanıcıya kayıt e-postası gönderilecektir.`,
      type: 'approve',
      confirmText: 'Onayla'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(pendingUser.id));
    try {
      await pendingUserService.update(pendingUser.id, { status: 'approved' });
      toast.success(`${pendingUser.firstName} ${pendingUser.lastName} onaylandı ve kayıt e-postası gönderildi.`);
      await fetchPendingUsers();
    } catch (error) {
      console.error('Error approving user:', error);
      toast.error('Kullanıcı onaylanırken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(pendingUser.id);
        return newSet;
      });
    }
  };

  // Reject user
  const handleRejectUser = async (pendingUser: PendingUser) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Reddet',
      message: `${pendingUser.firstName} ${pendingUser.lastName} kullanıcısının başvurusunu reddetmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      type: 'disapprove',
      confirmText: 'Reddet'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(pendingUser.id));
    try {
      await pendingUserService.update(pendingUser.id, { status: 'rejected' });
      toast.success(`${pendingUser.firstName} ${pendingUser.lastName} kullanıcısının başvurusu reddedildi.`);
      await fetchPendingUsers();
    } catch (error) {
      console.error('Error rejecting user:', error);
      toast.error('Kullanıcı reddedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(pendingUser.id);
        return newSet;
      });
    }
  };

  return (
    <>
      <ConfirmModal />
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {/* Table Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Bekleyen Kullanıcılar ({pendingUsers.length})
          </h2>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-blue-50">
              <tr>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>Kullanıcı</span>
                  </div>
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4" />
                    <span>İletişim</span>
                  </div>
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>Başvuru Tarihi</span>
                  </div>
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2 justify-end">
                    <Clock className="w-4 h-4" />
                    <span>İşlemler</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={4} className="px-6 py-20 text-center">
                    <div className="flex items-center justify-center text-gray-500">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Yükleniyor...</span>
                    </div>
                  </td>
                </tr>
              ) : pendingUsers.length > 0 ? (
                pendingUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">{getInitials(user.firstName, user.lastName)}</span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{user.email}</div>
                      <div className="text-sm text-gray-500">{user.phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        {formatDateTime(user.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end space-x-2">
                        {processingIds.has(user.id) ? (
                          <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            <button
                              onClick={() => handleApproveUser(user)}
                              className="p-2 text-green-600 rounded-full hover:bg-green-100 transition-colors"
                              title="Onayla"
                            >
                              <CheckCircle className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => handleRejectUser(user)}
                              className="p-2 text-red-600 rounded-full hover:bg-red-100 transition-colors"
                              title="Reddet"
                            >
                              <XCircle className="w-5 h-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-20 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <User className="w-8 h-8 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Bekleyen kullanıcı yok
                      </h3>
                      <p>
                        Şu anda onay bekleyen yeni kullanıcı başvurusu bulunmuyor.
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}