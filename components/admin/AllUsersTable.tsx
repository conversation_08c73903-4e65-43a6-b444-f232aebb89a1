'use client';

import { useState, useEffect } from 'react';
import { User, Mail, Calendar, Shield, UserCheck, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { User as UserType } from '@/lib/types';
import { userService } from '@/lib/services/userService';
import { formatDateTime } from '@/lib/utils';
import { useUser } from '@/lib/hooks/useUser';
import { useDentoConfirm } from '@/lib/hooks/useDentoConfirm';
import UserDetailsModal from './UserDetailsModal';


// Helper function to get initials
const getInitials = (firstName: string, lastName: string) => {
  return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
};

// Helper function to get role badge
const getRoleBadge = (role: string) => {
  const roleConfig = {
    doctor: { label: 'Doktor', color: 'bg-blue-100 text-blue-800' },
    assistant: { label: 'Asistan', color: 'bg-green-100 text-green-800' }
  };

  const config = roleConfig[role as keyof typeof roleConfig] || { label: role, color: 'bg-gray-100 text-gray-800' };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

export default function AllUsersTable() {
  const [users, setUsers] = useState<UserType[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { userProfile } = useUser();
  const { confirm, ConfirmModal } = useDentoConfirm();

  // Fetch all users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const allUsers = await userService.getAll();
      setUsers(allUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // Delete user
  const handleDeleteUser = async (user: UserType) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Sil',
      message: `${user.firstName} ${user.lastName} kullanıcısını kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve kullanıcının tüm verileri silinecektir.`,
      type: 'delete',
      confirmText: 'Sil'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(user.id));
    try {
      await userService.delete(user.id);
      toast.success(`${user.firstName} ${user.lastName} kullanıcısı başarıyla silindi.`);
      await fetchUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Kullanıcı silinirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(user.id);
        return newSet;
      });
    }
  };

  const isCurrentUser = (userId: string) => {
    return userProfile?.id === userId;
  };

  const handleUserClick = (user: UserType) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  return (
    <>
      <ConfirmModal />
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">
          Tüm Kullanıcılar ({users.length})
        </h2>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-blue-50">
            <tr>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <span>Kullanıcı</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>İletişim</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Rol</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>Kayıt Tarihi</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2 justify-end">
                  <UserCheck className="w-4 h-4" />
                  <span>İşlemler</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={5} className="px-6 py-20 text-center">
                  <div className="flex items-center justify-center text-gray-500">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Yükleniyor...</span>
                  </div>
                </td>
              </tr>
            ) : users.length > 0 ? (
              users.map((user) => (
                <tr 
                  key={user.id} 
                  className="hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => handleUserClick(user)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">{getInitials(user.firstName, user.lastName)}</span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                          {isCurrentUser(user.id) && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Siz
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{user.email}</div>
                    <div className="text-sm text-gray-500">{user.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getRoleBadge(user.role)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDateTime(user.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end">
                      {processingIds.has(user.id) ? (
                        <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteUser(user);
                        }}
                          className="p-2 text-red-600 rounded-full hover:bg-red-100 transition-colors"
                          title="Kullanıcıyı Sil"
                          disabled={isCurrentUser(user.id)}
                        >
                          <Trash2 className={`w-5 h-5 ${isCurrentUser(user.id) ? 'opacity-30' : ''}`} />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-20 text-center text-gray-500">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <User className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Kullanıcı bulunamadı
                    </h3>
                    <p>
                      Henüz sistemde kayıtlı kullanıcı bulunmuyor.
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* User Details Modal */}
      <UserDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        user={selectedUser}
      />
    </div>
    </>
  );
} 