'use client';

import { X, User, Mail, Calendar, Shield } from 'lucide-react';
import { User as UserType } from '@/lib/types';
import { formatDateTime } from '@/lib/utils';

const Section: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode; className?: string }> = ({ icon, title, children, className }) => (
  <div className={`bg-white rounded-xl p-5 border border-gray-200/80 shadow-sm ${className}`}>
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center text-gray-500">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
    </div>
    <div>
      {children}
    </div>
  </div>
);

const InfoItem: React.FC<{ label: string; value: React.ReactNode; }> = ({ label, value }) => (
  <div className="py-2 border-b border-gray-100 last:border-b-0">
    <p className="text-sm font-medium text-gray-600 mb-0.5">{label}</p>
    <div className="text-sm text-gray-800">{value}</div>
  </div>
);

export interface UserDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserType | null;
}



// Helper function to get role badge
const getRoleBadge = (role: string) => {
  const roleConfig = {
    doctor: { label: 'Doktor', color: 'bg-blue-100 text-blue-800' },
    assistant: { label: 'Asistan', color: 'bg-green-100 text-green-800' }
  };

  const config = roleConfig[role as keyof typeof roleConfig] || { label: role, color: 'bg-gray-100 text-gray-800' };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

export default function UserDetailsModal({ isOpen, onClose, user }: UserDetailsModalProps) {
  if (!isOpen || !user) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={onClose} />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-gray-50 rounded-2xl shadow-2xl transform transition-all animate-slideUp">
      {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-t-2xl px-6 py-4 sticky top-0 z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-white">Kullanıcı Detayları</h2>
                  <p className="text-blue-100 text-sm">{user.firstName} {user.lastName}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="w-9 h-9 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors cursor-pointer"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-5 max-h-[70vh] overflow-y-auto scrollbar-hide">
            {/* Basic Information */}
            <Section icon={<User className="w-5 h-5" />} title="Temel Bilgiler">
              <InfoItem label="Ad" value={user.firstName} />
              <InfoItem label="Soyad" value={user.lastName} />
              <InfoItem label="Rol" value={getRoleBadge(user.role)} />
              <InfoItem label="Klinik" value={user.clinic} />
            </Section>

            {/* Contact Information */}
            <Section icon={<Mail className="w-5 h-5" />} title="İletişim Bilgileri">
              <InfoItem label="E-posta" value={user.email} />
              <InfoItem label="Telefon" value={user.phone} />
              <InfoItem label="Adres" value={user.address || 'Adres bilgisi girilmemiş'} />
            </Section>

            {/* Personal Information */}
            <Section icon={<Calendar className="w-5 h-5" />} title="Kişisel Bilgiler">
              <InfoItem 
                label="Doğum Tarihi" 
                value={user.birthDate ? formatDateTime(user.birthDate) : 'Belirtilmemiş'} 
              />
            </Section>

            {/* System Information */}
            <Section icon={<Shield className="w-5 h-5" />} title="Sistem Bilgileri">
              <InfoItem label="Kullanıcı ID" value={<span className="font-mono">{user.id}</span>} />
              <InfoItem label="Kayıt Tarihi" value={formatDateTime(user.createdAt)} />
              <InfoItem label="Son Güncelleme" value={formatDateTime(user.updatedAt)} />
            </Section>
          </div>
        </div>
      </div>
    </div>
  );
}