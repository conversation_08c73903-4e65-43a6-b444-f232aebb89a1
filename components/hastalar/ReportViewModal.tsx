'use client';

import { useState, useRef } from 'react';
import { X, Edit, Save, Trash2, FileText, Eye, Printer } from 'lucide-react';
import { PatientImage } from '@/lib/types';
import { useUser } from '@/lib/hooks/useUser';
import { updatePatientImage } from '@/lib/services/patientImageService';
import { StorageService } from '@/lib/services/storageService';
import { toast } from 'sonner';
import RichTextEditor from '@/components/ui/RichTextEditor';

interface ReportViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  image: PatientImage;
  onUpdate?: () => void;
}

export default function ReportViewModal({ isOpen, onClose, image, onUpdate }: ReportViewModalProps) {
  const { isAdmin } = useUser();
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(image.reportContent || '');
  const [newReportFile, setNewReportFile] = useState<File | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  if (!isOpen || !image.reportType) return null;

  const handleSaveChanges = async () => {
    if (!isAdmin) return;

    setIsUpdating(true);
    try {
      const updateData: Partial<PatientImage> = {};

      if (image.reportType === 'richtext') {
        updateData.reportContent = editedContent;
      } else if (image.reportType === 'pdf' && newReportFile) {
        // Upload new PDF
        const reportResult = await StorageService.uploadReportPDF(
          newReportFile,
          image.uploadedBy,
          image.patientId,
          image.id!
        );
        
        // Delete old PDF if exists
        if (image.reportPath) {
          try {
            await StorageService.deleteReportPDF(image.reportPath);
          } catch (error) {
            console.warn('Could not delete old report PDF:', error);
          }
        }

        updateData.reportUrl = reportResult.url;
        updateData.reportPath = reportResult.path;
        updateData.reportFilename = newReportFile.name;
      }

      if (Object.keys(updateData).length > 0) {
        await updatePatientImage(image.id!, updateData);
        toast.success('Rapor başarıyla güncellendi');
        onUpdate?.();
      }

      setIsEditing(false);
      setNewReportFile(null);
    } catch (error) {
      console.error('Error updating report:', error);
      toast.error('Rapor güncellenirken bir hata oluştu');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteReport = async () => {
    if (!isAdmin) return;

    if (!confirm('Bu raporu silmek istediğinizden emin misiniz?')) return;

    setIsUpdating(true);
    try {
      // Delete PDF file if exists
      if (image.reportPath) {
        try {
          await StorageService.deleteReportPDF(image.reportPath);
        } catch (error) {
          console.warn('Could not delete report PDF:', error);
        }
      }

      // Update image record to remove report fields
      await updatePatientImage(image.id!, {
        reportType: undefined,
        reportContent: undefined,
        reportUrl: undefined,
        reportPath: undefined,
        reportFilename: undefined,
      });

      toast.success('Rapor başarıyla silindi');
      onUpdate?.();
      onClose();
    } catch (error) {
      console.error('Error deleting report:', error);
      toast.error('Rapor silinirken bir hata oluştu');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDownloadPDF = () => {
    if (image.reportUrl) {
      window.open(image.reportUrl, '_blank');
    }
  };



  const handlePrint = () => {
    if (image.reportType === 'pdf' && image.reportUrl) {
      // Open PDF in new window for printing
      const printWindow = window.open(image.reportUrl, '_blank');
      printWindow?.addEventListener('load', () => {
        printWindow.print();
      });
    } else if (image.reportType === 'richtext') {
      // Create a printable version of rich text content
      const printContent = `
        <html>
          <head>
            <title>Rapor - ${image.filename}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
              .content { line-height: 1.6; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Rapor</h1>
              <p><strong>Dosya:</strong> ${image.filename}</p>
              <p><strong>Tarih:</strong> ${new Date(image.createdAt).toLocaleDateString('tr-TR')}</p>
            </div>
            <div class="content">
              ${image.reportContent || ''}
            </div>
          </body>
        </html>
      `;

      const printWindow = window.open('', '_blank');
      printWindow?.document.write(printContent);
      printWindow?.document.close();
      printWindow?.print();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = StorageService.validatePDFFile(file);
    if (validation.isValid) {
      setNewReportFile(file);
    } else {
      toast.error(validation.error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white">Rapor Görüntüle</h2>
                <p className="text-blue-100 text-sm">{image.filename}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {/* Action buttons */}
              <button
                onClick={handlePrint}
                className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors"
                title="Yazdır"
              >
                <Printer className="w-4 h-4" />
              </button>
              


              {isAdmin && (
                <>
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors"
                      title="Düzenle"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  ) : (
                    <button
                      onClick={handleSaveChanges}
                      disabled={isUpdating}
                      className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"
                      title="Kaydet"
                    >
                      <Save className="w-4 h-4" />
                    </button>
                  )}
                  
                  <button
                    onClick={handleDeleteReport}
                    disabled={isUpdating}
                    className="p-2 text-white/80 hover:text-white hover:bg-red-200 rounded-lg transition-colors disabled:opacity-50"
                    title="Sil"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </>
              )}

              <button
                onClick={onClose}
                className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {image.reportType === 'pdf' ? (
            <div>
              {isEditing && isAdmin ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Yeni PDF Dosyası
                    </label>
                    <div
                      onClick={() => fileInputRef.current?.click()}
                      className="border-2 border-dashed border-blue-300 rounded-xl p-6 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                    >
                      <FileText className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-700 mb-1">
                        {newReportFile ? newReportFile.name : 'Yeni PDF dosyası seçin'}
                      </p>
                      <p className="text-xs text-gray-500">
                        Maksimum 20MB
                      </p>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="application/pdf"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <p><strong>Mevcut dosya:</strong> {image.reportFilename}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-8 h-8 text-red-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">PDF Raporu</h3>
                  <p className="text-gray-600 mb-4">{image.reportFilename}</p>
                  <div className="flex justify-center">
                    <button
                      onClick={handleDownloadPDF}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Görüntüle
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div>
              {isEditing && isAdmin ? (
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Rapor İçeriği
                  </label>
                  <RichTextEditor
                    content={editedContent}
                    onChange={setEditedContent}
                    placeholder="Rapor içeriğini düzenleyin..."
                  />
                </div>
              ) : (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Rapor İçeriği</h3>
                  <div className="prose prose-sm max-w-none">
                    <RichTextEditor
                      content={image.reportContent || ''}
                      onChange={() => {}} // Read-only
                      readOnly={true}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
