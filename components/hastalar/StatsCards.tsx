'use client';

import { Patient } from '@/lib/types';
import { Users, UserPlus, Calendar, CheckSquare } from 'lucide-react';

interface StatsCardsProps {
  patients: Patient[];
  totalPatients: number;
}

export default function StatsCards({ patients, totalPatients }: StatsCardsProps) {
  const newThisMonth = patients.filter(p => new Date(p.createdAt).getMonth() === new Date().getMonth()).length;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Toplam Hasta */}
      <div className="group relative bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-2xl shadow-lg border border-blue-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
            <Users className="w-7 h-7 text-white" />
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+5%</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Toplam Hasta</h3>
          <p className="text-4xl font-bold text-gray-900">{totalPatients}</p>
        </div>
      </div>

      {/* Aktif Hasta */}
      <div className="group relative bg-gradient-to-br from-emerald-50 via-white to-emerald-50 rounded-2xl shadow-lg border border-emerald-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl shadow-lg">
            <CheckSquare className="w-7 h-7 text-white" />
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+8%</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Aktif Hasta</h3>
          <p className="text-4xl font-bold text-gray-900">{totalPatients}</p>
        </div>
      </div>

      {/* Bu Ayki Yeni Hastalar */}
      <div className="group relative bg-gradient-to-br from-purple-50 via-white to-purple-50 rounded-2xl shadow-lg border border-purple-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
            <UserPlus className="w-7 h-7 text-white" />
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-blue-600 bg-blue-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+{newThisMonth}</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Bu Ay Yeni</h3>
          <p className="text-4xl font-bold text-gray-900">{newThisMonth}</p>
        </div>
      </div>

      {/* Bekleyen Randevular (Örnek) */}
      <div className="group relative bg-gradient-to-br from-orange-50 via-white to-orange-50 rounded-2xl shadow-lg border border-orange-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
            <Calendar className="w-7 h-7 text-white" />
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-orange-600 bg-orange-100/80 px-3 py-1 rounded-full backdrop-blur-sm">12</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Bekleyen Randevu</h3>
          <p className="text-4xl font-bold text-gray-900">12</p>
        </div>
      </div>
    </div>
  );
}