'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { X, Upload, Camera, FileText, Clock, User, CheckCircle, AlertCircle, ArrowRight, ImageIcon, Trash2, FileUp, Type } from 'lucide-react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useUser } from '@/lib/hooks/useUser';
import { IstemFormuData, Patient, PatientImage, ReportType } from '@/lib/types';
import { getWaitingIstemFormuByPatient, completeIstemFormu } from '@/lib/services/istemFormuService';
import { StorageService } from '@/lib/services/storageService';
import { addPatientImage } from '@/lib/services/patientImageService';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';
import RichTextEditor from '@/components/ui/RichTextEditor';

interface ScanUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  patient: Patient;
  onUploadComplete?: () => void;
}

type UploadMode = 'selection' | 'with-form' | 'without-form';

export default function ScanUploadModal({ isOpen, onClose, patient, onUploadComplete }: ScanUploadModalProps) {
  const { user } = useAuth();
  const { isAdmin } = useUser();
  const [waitingForms, setWaitingForms] = useState<IstemFormuData[]>([]);
  const [selectedForm, setSelectedForm] = useState<IstemFormuData | null>(null);
  const [selectedFilesByType, setSelectedFilesByType] = useState<{ [key: string]: File[] }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [uploadMode, setUploadMode] = useState<UploadMode>('selection');
  const [standaloneFiles, setStandaloneFiles] = useState<File[]>([]);
  const [standaloneNotes, setStandaloneNotes] = useState('');
  const [standaloneScanType, setStandaloneScanType] = useState('');
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});
  const standaloneFileInputRef = useRef<HTMLInputElement | null>(null);

  // Report attachment states for standalone uploads
  const [hasReport, setHasReport] = useState(false);
  const [reportType, setReportType] = useState<ReportType>('richtext');
  const [reportContent, setReportContent] = useState('');
  const [reportFile, setReportFile] = useState<File | null>(null);
  const reportFileInputRef = useRef<HTMLInputElement | null>(null);

  // Report attachment states for form-based uploads (per scan type)
  const [formReports, setFormReports] = useState<{[scanType: string]: {
    hasReport: boolean;
    reportType: ReportType;
    reportContent: string;
    reportFile: File | null;
  }}>({});
  const formReportFileInputRefs = useRef<{[scanType: string]: HTMLInputElement | null}>({});

  const handleClose = () => {
    setUploadMode('selection');
    setSelectedForm(null);
    setSelectedFilesByType({});
    setUploadProgress({});
    setStandaloneFiles([]);
    setStandaloneNotes('');
    setStandaloneScanType('');
    // Reset report states
    setHasReport(false);
    setReportType('richtext');
    setReportContent('');
    setReportFile(null);
    setFormReports({});
    onClose();
  };

  const fetchWaitingForms = useCallback(async () => {
    if (!patient?.id) return;
    
    setIsLoading(true);
    try {
      const forms = await getWaitingIstemFormuByPatient(patient.id);
      setWaitingForms(forms);
    } catch (error) {
      console.error('Error fetching waiting forms:', error);
      toast.error('Bekleyen formlar yüklenirken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }, [patient?.id]);

  // Fetch waiting forms when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchWaitingForms();
      setUploadMode('selection');
      setSelectedForm(null);
      setSelectedFilesByType({});
      setUploadProgress({});
      setStandaloneFiles([]);
      setStandaloneNotes('');
      setStandaloneScanType('');
      // Reset report states
      setHasReport(false);
      setReportType('richtext');
      setReportContent('');
      setReportFile(null);
      setFormReports({});
    }
  }, [isOpen, fetchWaitingForms]);

  // Initialize file arrays when form is selected
  useEffect(() => {
    if (selectedForm) {
      const initialFiles: { [key: string]: File[] } = {};
      selectedForm.xrayTypes.forEach(type => {
        initialFiles[type] = [];
      });
      setSelectedFilesByType(initialFiles);
    } else {
      setSelectedFilesByType({});
    }
  }, [selectedForm]);

  const handleFileSelect = (type: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    // Validate each file
    const validFiles: File[] = [];
    for (const file of files) {
      const validation = StorageService.validateScanFile(file);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        toast.error(`${file.name}: ${validation.error}`);
      }
    }
    
    setSelectedFilesByType(prev => ({
      ...prev,
      [type]: [...(prev[type] || []), ...validFiles]
    }));
  };

  const handleRemoveFile = (type: string, index: number) => {
    setSelectedFilesByType(prev => ({
      ...prev,
      [type]: prev[type].filter((_, i) => i !== index)
    }));
  };

  const handleStandaloneFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    // Validate each file
    const validFiles: File[] = [];
    for (const file of files) {
      const validation = StorageService.validateScanFile(file);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        toast.error(`${file.name}: ${validation.error}`);
      }
    }

    setStandaloneFiles(prev => [...prev, ...validFiles]);
  };

  const handleRemoveStandaloneFile = (index: number) => {
    setStandaloneFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleReportFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = StorageService.validatePDFFile(file);
    if (validation.isValid) {
      setReportFile(file);
    } else {
      toast.error(validation.error);
    }
  };

  const handleRemoveReportFile = () => {
    setReportFile(null);
    if (reportFileInputRef.current) {
      reportFileInputRef.current.value = '';
    }
  };

  // Form report helper functions
  const getFormReport = (scanType: string) => {
    return formReports[scanType] || {
      hasReport: false,
      reportType: 'richtext' as ReportType,
      reportContent: '',
      reportFile: null
    };
  };

  const updateFormReport = (scanType: string, updates: Partial<typeof formReports[string]>) => {
    setFormReports(prev => ({
      ...prev,
      [scanType]: {
        ...getFormReport(scanType),
        ...updates
      }
    }));
  };

  const handleFormReportFileSelect = (scanType: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = StorageService.validatePDFFile(file);
    if (validation.isValid) {
      updateFormReport(scanType, { reportFile: file });
    } else {
      toast.error(validation.error);
    }
  };

  const handleRemoveFormReportFile = (scanType: string) => {
    updateFormReport(scanType, { reportFile: null });
    if (formReportFileInputRefs.current[scanType]) {
      formReportFileInputRefs.current[scanType]!.value = '';
    }
  };

  const handleStandaloneUpload = async () => {
    if (!user || standaloneFiles.length === 0) return;

    // Validate report if present
    if (hasReport) {
      if (reportType === 'richtext' && !reportContent.trim()) {
        toast.error('Rapor içeriği boş olamaz');
        return;
      }
      if (reportType === 'pdf' && !reportFile) {
        toast.error('PDF dosyası seçilmedi');
        return;
      }
    }

    setIsUploading(true);
    try {
      const uploadedImages: string[] = [];

      // Upload files with progress tracking
      for (let i = 0; i < standaloneFiles.length; i++) {
        const file = standaloneFiles[i];
        const progressKey = `standalone-${file.name}`;
        setUploadProgress(prev => ({ ...prev, [progressKey]: 0 }));

        try {
          const result = await StorageService.uploadScanImage(
            file,
            user.uid,
            patient.id,
            standaloneScanType || undefined
          );

          // Prepare image data
          const imageData: Omit<PatientImage, 'id' | 'createdAt'> = {
            patientId: patient.id,
            uploadedBy: user.uid,
            imageUrl: result.url,
            imagePath: result.path,
            filename: result.filename,
            scanType: standaloneScanType,
            notes: standaloneNotes,
          };

          // Handle report attachment if present
          if (hasReport) {
            imageData.reportType = reportType;

            if (reportType === 'richtext' && reportContent.trim()) {
              imageData.reportContent = reportContent;
            } else if (reportType === 'pdf' && reportFile) {
              try {
                // Upload PDF report
                const reportResult = await StorageService.uploadReportPDF(
                  reportFile,
                  user.uid,
                  patient.id,
                  `temp-${Date.now()}` // Temporary ID, will be replaced with actual image ID
                );
                imageData.reportUrl = reportResult.url;
                imageData.reportPath = reportResult.path;
                imageData.reportFilename = reportFile.name;
              } catch (reportError) {
                console.error('Error uploading report PDF:', reportError);
                toast.error(`Rapor yüklenirken hata: ${file.name}`);
                // Continue with image upload without report
              }
            }
          }

          // Save image record to Firestore
          await addPatientImage(imageData);

          uploadedImages.push(result.url);
          setUploadProgress(prev => ({ ...prev, [progressKey]: 100 }));
        } catch (fileError) {
          console.error(`Error uploading file ${file.name}:`, fileError);
          toast.error(`Dosya yüklenirken hata: ${file.name}`);
          setUploadProgress(prev => ({ ...prev, [progressKey]: 0 }));
        }
      }

      toast.success(`${uploadedImages.length} görüntü başarıyla yüklendi`);

      // Notify parent component to refresh data
      onUploadComplete?.();

      // Reset state and close modal
      handleClose();

    } catch (error) {
      console.error('Error uploading standalone files:', error);
      toast.error('Görüntüler yüklenirken bir hata oluştu');
    } finally {
      setIsUploading(false);
    }
  };

  const handleUpload = async () => {
    if (!selectedForm || !user) {
      toast.error('Lütfen form seçin');
      return;
    }

    if (!isAdmin) {
      toast.error('Bu işlem için yönetici yetkisi gereklidir');
      return;
    }

    // Validate at least one file per type
    for (const type of selectedForm.xrayTypes) {
      if (!selectedFilesByType[type] || selectedFilesByType[type].length === 0) {
        toast.error(`Lütfen ${type} türü için en az bir dosya seçin`);
        return;
      }

      // Validate form reports if present
      const formReport = getFormReport(type);
      if (formReport.hasReport) {
        if (formReport.reportType === 'richtext' && !formReport.reportContent.trim()) {
          toast.error(`${type} için rapor içeriği boş olamaz`);
          return;
        }
        if (formReport.reportType === 'pdf' && !formReport.reportFile) {
          toast.error(`${type} için PDF dosyası seçilmedi`);
          return;
        }
      }
    }

    setIsUploading(true);
    try {
      const uploadedImages: string[] = [];
      
      // Upload files per type with progress tracking
      for (const type of selectedForm.xrayTypes) {
        const files = selectedFilesByType[type];
        const formReport = getFormReport(type);

        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const progressKey = `${type}-${file.name}`;
          setUploadProgress(prev => ({ ...prev, [progressKey]: 0 }));

          try {
            const result = await StorageService.uploadScanImage(
              file,
              selectedForm.userId,
              patient.id,
              type
            );

            // Prepare image data
            const imageData: Omit<PatientImage, 'id' | 'createdAt'> = {
              patientId: patient.id,
              uploadedBy: user.uid,
              imageUrl: result.url,
              imagePath: result.path,
              filename: result.filename,
              scanType: type,
              notes: `İstem Formu: ${selectedForm.diagnosis}`,
            };

            // Handle report attachment if present for this scan type
            if (formReport.hasReport) {
              imageData.reportType = formReport.reportType;

              if (formReport.reportType === 'richtext' && formReport.reportContent.trim()) {
                imageData.reportContent = formReport.reportContent;
              } else if (formReport.reportType === 'pdf' && formReport.reportFile) {
                try {
                  // Upload PDF report
                  const reportResult = await StorageService.uploadReportPDF(
                    formReport.reportFile,
                    user.uid,
                    patient.id,
                    `temp-${Date.now()}` // Temporary ID, will be replaced with actual image ID
                  );
                  imageData.reportUrl = reportResult.url;
                  imageData.reportPath = reportResult.path;
                  imageData.reportFilename = formReport.reportFile.name;
                } catch (reportError) {
                  console.error('Error uploading report PDF:', reportError);
                  toast.error(`${type} raporu yüklenirken hata: ${file.name}`);
                  // Continue with image upload without report
                }
              }
            }

            // Save image record to Firestore
            await addPatientImage(imageData);

            uploadedImages.push(result.url);
            setUploadProgress(prev => ({ ...prev, [progressKey]: 100 }));
          } catch (fileError) {
            console.error(`Error uploading file ${file.name}:`, fileError);
            toast.error(`Dosya yüklenirken hata: ${file.name}`);
            setUploadProgress(prev => ({ ...prev, [progressKey]: 0 }));
          }
        }
      }
      
      // Update the form status to completed
      await completeIstemFormu(selectedForm.id!, selectedForm.userId, uploadedImages);
      
      toast.success('Görüntüler başarıyla yüklendi ve form tamamlandı');

      // Notify parent component to refresh data
      onUploadComplete?.();

      // Reset state and close modal
      handleClose();
      
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Görüntüler yüklenirken bir hata oluştu');
    } finally {
      setIsUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getXrayTypeBadges = (types: string[]) => {
    return types.map((type, index) => (
      <span 
        key={index}
        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
      >
        {type}
      </span>
    ));
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      'Normal': { color: 'bg-gray-100 text-gray-800', label: 'Normal' },
      'Acil': { color: 'bg-orange-100 text-orange-800', label: 'Acil' },
      'Çok Acil': { color: 'bg-red-100 text-red-800', label: 'Çok Acil' },
      'Düşük': { color: 'bg-green-100 text-green-800', label: 'Düşük' }
    };
    
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.Normal;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getTotalSelectedFiles = () => {
    if (uploadMode === 'without-form') {
      return standaloneFiles.length;
    }
    return Object.values(selectedFilesByType).reduce((total, files) => total + files.length, 0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={handleClose} />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl transform transition-all animate-slideUp">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-t-2xl px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                  <Camera className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Tarama Görüntüsü Yükle</h2>
                  <p className="text-blue-100 text-sm">
                    {patient.firstName} {patient.lastName} için görüntü yükleme
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center hover:bg-white/30 transition-colors text-white"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

        {/* Content */}
        <div className="p-8">
          {!isAdmin ? (
            <div className="text-center py-12">
              <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                Yetkisiz Erişim
              </h3>
              <p className="text-gray-500">
                Görüntü yükleme işlemi sadece yöneticiler tarafından gerçekleştirilebilir.
              </p>
            </div>
          ) : uploadMode === 'selection' ? (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Yükleme Türünü Seçin</h3>
                <p className="text-gray-600">Görüntüleri nasıl yüklemek istiyorsunuz?</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Upload with waiting forms */}
                <div
                  onClick={() => waitingForms.length > 0 && setUploadMode('with-form')}
                  className={`relative p-8 rounded-2xl border-2 transition-all duration-300 cursor-pointer ${
                    waitingForms.length > 0
                      ? 'border-blue-200 hover:border-blue-400 hover:shadow-lg bg-gradient-to-br from-blue-50 via-white to-blue-50'
                      : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                  }`}
                >
                  <div className="text-center">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center ${
                      waitingForms.length > 0
                        ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                        : 'bg-gray-400'
                    }`}>
                      <FileText className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Bekleyen İstem Formu ile</h4>
                    <p className="text-gray-600 mb-4">
                      Mevcut bekleyen istem formlarından birini seçerek görüntü yükleyin
                    </p>
                    {waitingForms.length > 0 ? (
                      <div className="inline-flex items-center justify-center w-8 h-8 bg-blue-500 text-white rounded-full text-sm font-bold">
                        {waitingForms.length}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">Bekleyen form bulunmuyor</p>
                    )}
                  </div>
                  {waitingForms.length > 0 && (
                    <ArrowRight className="absolute top-4 right-4 w-5 h-5 text-blue-500" />
                  )}
                </div>

                {/* Upload without form */}
                <div
                  onClick={() => setUploadMode('without-form')}
                  className="relative p-8 rounded-2xl border-2 border-emerald-200 hover:border-emerald-400 hover:shadow-lg bg-gradient-to-br from-emerald-50 via-white to-emerald-50 transition-all duration-300 cursor-pointer"
                >
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <ImageIcon className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">İstem Formu Olmadan</h4>
                    <p className="text-gray-600 mb-4">
                      Doğrudan görüntü yükleyin, istem formu gerektirmez
                    </p>
                    <div className="inline-flex items-center text-emerald-600 font-medium">
                      <span>Hemen yükle</span>
                    </div>
                  </div>
                  <ArrowRight className="absolute top-4 right-4 w-5 h-5 text-emerald-500" />
                </div>
              </div>
            </div>
          ) : isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-3 text-gray-600">Bekleyen formlar yükleniyor...</span>
            </div>
          ) : uploadMode === 'with-form' && waitingForms.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                Bekleyen İşlem Bulunamadı
              </h3>
              <p className="text-gray-500">
                Bu hasta için bekleyen radyolojik görüntüleme isteği bulunmamaktadır.
              </p>
              <button
                onClick={() => setUploadMode('selection')}
                className="mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Geri Dön
              </button>
            </div>
          ) : uploadMode === 'with-form' ? (
            <div className="space-y-6">
              {/* Form Selection */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-blue-500" />
                  Bekleyen İstem Formları ({waitingForms.length})
                </h3>
                
                <div className="grid gap-4">
                  {waitingForms.map((form) => (
                    <div
                      key={form.id}
                      onClick={() => setSelectedForm(form)}
                      className={`p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                        selectedForm?.id === form.id
                          ? 'border-blue-500 bg-blue-50 shadow-lg'
                          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="font-semibold text-gray-900">{form.diagnosis}</h4>
                            {getPriorityBadge(form.priorityStatus)}
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{form.notes}</p>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-1" />
                              {formatDate(form.createdAt)}
                            </div>
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-1" />
                              İstem Sahibi
                            </div>
                          </div>
                        </div>
                        
                        {selectedForm?.id === form.id && (
                          <CheckCircle className="w-6 h-6 text-blue-500" />
                        )}
                      </div>
                      
                      {form.xrayTypes.length > 0 && (
                        <div>
                          <span className="text-sm font-medium text-gray-700 block mb-2">
                            İstenen Görüntüleme Türleri:
                          </span>
                          <div className="flex flex-wrap gap-2">
                            {getXrayTypeBadges(form.xrayTypes)}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* File Upload Section */}
              {selectedForm && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Upload className="w-5 h-5 mr-2 text-green-500" />
                    Görüntü Dosyalarını Seç
                  </h3>
                  
                  <div className="space-y-6">
                    {selectedForm.xrayTypes.map(type => (
                      <div key={type}>
                        <h4 className="font-medium text-gray-900 mb-3">{type} Görüntüleri</h4>
                        
                        {/* File Input */}
                        <div
                          onClick={() => fileInputRefs.current[type]?.click()}
                          className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 mb-4"
                        >
                          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-lg font-medium text-gray-700 mb-2">
                            {type} dosyalarını buraya sürükleyin veya seçmek için tıklayın
                          </p>
                          <p className="text-sm text-gray-500">
                            JPEG, PNG, WebP, TIFF formatları desteklenir (Maks. 10MB)
                          </p>
                        </div>
                        
                        <input
                          ref={(el) => { fileInputRefs.current[type] = el; }}
                          type="file"
                          multiple
                          accept="image/jpeg,image/jpg,image/png,image/webp,image/tiff"
                          onChange={(e) => handleFileSelect(type, e)}
                          className="hidden"
                        />

                        {/* Selected Files */}
                        {selectedFilesByType[type]?.length > 0 && (
                          <div className="space-y-2 mt-4">
                            <h5 className="font-medium text-gray-700">Seçilen {type} Dosyaları ({selectedFilesByType[type].length})</h5>
                            {selectedFilesByType[type].map((file, index) => (
                              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <Camera className="w-4 h-4 text-blue-600" />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                                  </div>
                                </div>

                                <button
                                  onClick={() => handleRemoveFile(type, index)}
                                  className="w-6 h-6 bg-red-100 hover:bg-red-200 rounded-full flex items-center justify-center transition-colors"
                                >
                                  <X className="w-3 h-3 text-red-600" />
                                </button>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Report Attachment Section for this scan type */}
                        <div className="mt-6 p-4 bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-xl border border-blue-200">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                <FileText className="w-3 h-3 text-blue-600" />
                              </div>
                              <h5 className="text-sm font-semibold text-gray-900">{type} Raporu (Opsiyonel)</h5>
                            </div>
                            <button
                              type="button"
                              onClick={() => updateFormReport(type, { hasReport: !getFormReport(type).hasReport })}
                              className={`px-3 py-1.5 rounded-lg font-medium text-xs transition-all duration-200 border-2 ${
                                getFormReport(type).hasReport
                                  ? 'border-blue-500 bg-blue-500 text-white shadow-md shadow-blue-200'
                                  : 'border-gray-300 bg-white text-gray-700 hover:border-blue-400 hover:bg-blue-50'
                              }`}
                            >
                              {getFormReport(type).hasReport ? 'Rapor Eklendi' : 'Rapor Ekle'}
                            </button>
                          </div>

                          {getFormReport(type).hasReport && (
                            <div className="space-y-3">
                              {/* Report Type Selection */}
                              <div>
                                <label className="block text-xs font-semibold text-gray-700 mb-2">
                                  Rapor Türü
                                </label>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                  <button
                                    type="button"
                                    onClick={() => updateFormReport(type, { reportType: 'richtext' })}
                                    disabled={isUploading}
                                    className={`p-3 rounded-lg border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                                      getFormReport(type).reportType === 'richtext'
                                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                                        : 'border-gray-200 hover:border-blue-300 text-gray-700'
                                    }`}
                                  >
                                    <Type className="w-4 h-4 mx-auto mb-1" />
                                    <div className="text-xs font-medium">Metin Editörü</div>
                                  </button>
                                  <button
                                    type="button"
                                    onClick={() => updateFormReport(type, { reportType: 'pdf' })}
                                    disabled={isUploading}
                                    className={`p-3 rounded-lg border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                                      getFormReport(type).reportType === 'pdf'
                                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                                        : 'border-gray-200 hover:border-blue-300 text-gray-700'
                                    }`}
                                  >
                                    <FileUp className="w-4 h-4 mx-auto mb-1" />
                                    <div className="text-xs font-medium">PDF Yükle</div>
                                  </button>
                                </div>
                              </div>

                              {/* Report Content */}
                              {getFormReport(type).reportType === 'richtext' && (
                                <div>
                                  <label className="block text-xs font-semibold text-gray-700 mb-1">
                                    Rapor İçeriği
                                  </label>
                                  <RichTextEditor
                                    content={getFormReport(type).reportContent}
                                    onChange={(content) => updateFormReport(type, { reportContent: content })}
                                    placeholder={`${type} rapor içeriğini yazın...`}
                                    className="min-h-[150px]"
                                  />
                                </div>
                              )}

                              {getFormReport(type).reportType === 'pdf' && (
                                <div>
                                  <label className="block text-xs font-semibold text-gray-700 mb-1">
                                    PDF Dosyası
                                  </label>
                                  {!getFormReport(type).reportFile ? (
                                    <div
                                      onClick={() => formReportFileInputRefs.current[type]?.click()}
                                      className="border-2 border-dashed border-blue-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                                    >
                                      <FileUp className="w-6 h-6 text-blue-400 mx-auto mb-1" />
                                      <p className="text-xs font-medium text-gray-700 mb-1">
                                        PDF dosyasını seçin
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        Maksimum 20MB
                                      </p>
                                    </div>
                                  ) : (
                                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                                      <div className="flex items-center space-x-2">
                                        <FileText className="w-6 h-6 text-blue-600" />
                                        <div>
                                          <p className="text-xs font-medium text-gray-900">{getFormReport(type).reportFile!.name}</p>
                                          <p className="text-xs text-gray-500">
                                            {(getFormReport(type).reportFile!.size / (1024 * 1024)).toFixed(2)} MB
                                          </p>
                                        </div>
                                      </div>
                                      <button
                                        type="button"
                                        onClick={() => handleRemoveFormReportFile(type)}
                                        className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                                      >
                                        <Trash2 className="w-3 h-3" />
                                      </button>
                                    </div>
                                  )}
                                  <input
                                    ref={(el) => { formReportFileInputRefs.current[type] = el; }}
                                    type="file"
                                    accept="application/pdf"
                                    onChange={(e) => handleFormReportFileSelect(type, e)}
                                    className="hidden"
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}

                    {/* Upload Progress */}
                    {isUploading && (
                      <div className="space-y-2 mt-6">
                        <h4 className="font-medium text-gray-900">Yükleme İlerlemesi</h4>
                        {Object.entries(uploadProgress).map(([key, progress]) => (
                          <div key={key} className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-700">{key}</span>
                              <span className="text-gray-500">{progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : uploadMode === 'without-form' ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-bold text-gray-900">Doğrudan Görüntü Yükle</h3>
                  <p className="text-gray-600">İstem formu olmadan görüntü yükleyin</p>
                </div>
                <button
                  onClick={() => setUploadMode('selection')}
                  className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  Geri Dön
                </button>
              </div>

              {/* Scan Type and Notes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Tarama Türü (Opsiyonel)
                  </label>
                  <input
                    type="text"
                    value={standaloneScanType}
                    onChange={(e) => setStandaloneScanType(e.target.value)}
                    placeholder="Örn: Panoramik, Bitewing, Periapikal"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Notlar (Opsiyonel)
                  </label>
                  <input
                    type="text"
                    value={standaloneNotes}
                    onChange={(e) => setStandaloneNotes(e.target.value)}
                    placeholder="Görüntü hakkında notlar"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all"
                  />
                </div>
              </div>

              {/* File Upload Area */}
              <div
                onClick={() => standaloneFileInputRef.current?.click()}
                className="border-2 border-dashed border-emerald-300 rounded-xl p-8 text-center cursor-pointer hover:border-emerald-400 hover:bg-emerald-50 transition-all duration-200 mb-4"
              >
                <Upload className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  Görüntü dosyalarını buraya sürükleyin veya seçmek için tıklayın
                </p>
                <p className="text-sm text-gray-500">
                  JPEG, PNG, WebP, TIFF formatları desteklenir (Maks. 10MB)
                </p>
              </div>

              <input
                ref={standaloneFileInputRef}
                type="file"
                multiple
                accept="image/jpeg,image/jpg,image/png,image/webp,image/tiff"
                onChange={handleStandaloneFileSelect}
                className="hidden"
              />

              {/* Report Attachment Section */}
              <div className="mt-8 p-6 bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">Rapor Eki (Opsiyonel)</h4>
                  </div>
                  <button
                    type="button"
                    onClick={() => setHasReport(!hasReport)}
                    className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 border-2 ${
                      hasReport
                        ? 'border-blue-500 bg-blue-500 text-white shadow-lg shadow-blue-200'
                        : 'border-gray-300 bg-white text-gray-700 hover:border-blue-400 hover:bg-blue-50'
                    }`}
                  >
                    {hasReport ? 'Rapor Eklendi' : 'Rapor Ekle'}
                  </button>
                </div>

                {hasReport && (
                  <div className="space-y-4">
                    {/* Report Type Selection */}
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">
                        Rapor Türü
                      </label>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <button
                          type="button"
                          onClick={() => setReportType('richtext')}
                          disabled={isUploading}
                          className={`p-4 rounded-xl border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                            reportType === 'richtext'
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-200 hover:border-blue-300 text-gray-700'
                          }`}
                        >
                          <Type className="w-6 h-6 mx-auto mb-2" />
                          <div className="text-sm font-medium">Metin Editörü</div>
                          <div className="text-xs text-gray-500 mt-1">Zengin metin içeriği</div>
                        </button>
                        <button
                          type="button"
                          onClick={() => setReportType('pdf')}
                          disabled={isUploading}
                          className={`p-4 rounded-xl border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                            reportType === 'pdf'
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-200 hover:border-blue-300 text-gray-700'
                          }`}
                        >
                          <FileUp className="w-6 h-6 mx-auto mb-2" />
                          <div className="text-sm font-medium">PDF Yükle</div>
                          <div className="text-xs text-gray-500 mt-1">PDF dosyası</div>
                        </button>
                      </div>
                    </div>

                    {/* Report Content */}
                    {reportType === 'richtext' && (
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Rapor İçeriği
                        </label>
                        <RichTextEditor
                          content={reportContent}
                          onChange={setReportContent}
                          placeholder="Rapor içeriğini yazın..."
                          className="min-h-[200px]"
                        />
                      </div>
                    )}

                    {reportType === 'pdf' && (
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          PDF Dosyası
                        </label>
                        {!reportFile ? (
                          <div
                            onClick={() => reportFileInputRef.current?.click()}
                            className="border-2 border-dashed border-blue-300 rounded-xl p-6 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                          >
                            <FileUp className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                            <p className="text-sm font-medium text-gray-700 mb-1">
                              PDF dosyasını seçin
                            </p>
                            <p className="text-xs text-gray-500">
                              Maksimum 20MB
                            </p>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl border border-blue-200">
                            <div className="flex items-center space-x-3">
                              <FileText className="w-8 h-8 text-blue-600" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">{reportFile.name}</p>
                                <p className="text-xs text-gray-500">
                                  {(reportFile.size / (1024 * 1024)).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={handleRemoveReportFile}
                              className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                        <input
                          ref={reportFileInputRef}
                          type="file"
                          accept="application/pdf"
                          onChange={handleReportFileSelect}
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Selected Files */}
              {standaloneFiles.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Seçilen Dosyalar ({standaloneFiles.length})</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {standaloneFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <ImageIcon className="w-5 h-5 text-emerald-500" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">{file.name}</p>
                            <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveStandaloneFile(index);
                          }}
                          className="p-1 text-red-500 hover:bg-red-50 rounded transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Yükleme İlerlemesi</h4>
                  {standaloneFiles.map((file, index) => {
                    const progressKey = `standalone-${file.name}`;
                    const progress = uploadProgress[progressKey] || 0;
                    return (
                      <div key={index} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">{file.name}</span>
                          <span className="text-gray-500">{progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ) : null}
        </div>

        {/* Footer */}
        {isAdmin && (uploadMode === 'with-form' && waitingForms.length > 0) && (
          <div className="bg-gray-50 px-8 py-6 flex items-center justify-between border-t">
            <div className="text-sm text-gray-600">
              {selectedForm ? 'Form seçildi' : 'Lütfen bir form seçin'} • {getTotalSelectedFiles()} dosya seçildi
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setUploadMode('selection')}
                disabled={isUploading}
                className="px-6 py-2 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                Geri
              </button>
              <button
                onClick={handleUpload}
                disabled={!selectedForm || getTotalSelectedFiles() === 0 || isUploading}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUploading ? 'Yükleniyor...' : 'Yükle ve Tamamla'}
              </button>
            </div>
          </div>
        )}

        {/* Footer for standalone upload */}
        {isAdmin && uploadMode === 'without-form' && (
          <div className="bg-gray-50 px-8 py-6 flex items-center justify-between border-t">
            <div className="text-sm text-gray-600">
              {getTotalSelectedFiles()} dosya seçildi
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setUploadMode('selection')}
                disabled={isUploading}
                className="px-6 py-2 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                Geri
              </button>
              <button
                onClick={handleStandaloneUpload}
                disabled={getTotalSelectedFiles() === 0 || isUploading}
                className="px-6 py-2 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUploading ? 'Yükleniyor...' : 'Görüntüleri Yükle'}
              </button>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
}