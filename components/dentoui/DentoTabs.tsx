'use client';

import { useRef, useEffect, ReactNode } from 'react';

interface Tab {
  id: string;
  label: string;
  icon: ReactNode;
}

interface DentoTabsProps {
  tabs: readonly Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const DentoTabs = ({ tabs, activeTab, onTabChange }: DentoTabsProps) => {
  const indicatorRef = useRef<HTMLDivElement>(null);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const moveIndicator = () => {
      const tabsContainer = tabsContainerRef.current;
      const indicator = indicatorRef.current;
      if (!tabsContainer || !indicator) return;

      const activeTabElement = tabsContainer.querySelector(`[data-tab-id="${activeTab}"]`);
      if (!activeTabElement) return;

      const tabRect = activeTabElement.getBoundingClientRect();
      const containerRect = tabsContainer.getBoundingClientRect();

      indicator.style.left = `${tabRect.left - containerRect.left}px`;
      indicator.style.width = `${tabRect.width}px`;
    };

    moveIndicator();
    window.addEventListener('resize', moveIndicator);
    
    return () => {
      window.removeEventListener('resize', moveIndicator);
    };
  }, [activeTab, tabs]);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-1 mb-8">
      <div ref={tabsContainerRef} className="relative flex space-x-1">
        {/* Animated Indicator */}
        <div
          ref={indicatorRef}
          className="absolute h-full bg-blue-600 rounded-lg transition-all duration-300 ease-out"
          style={{ top: 0 }}
        />

        {/* Tabs */}
        {tabs.map((tab) => (
          <button
            key={tab.id}
            data-tab-id={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`relative flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-colors duration-200 cursor-pointer ${
              activeTab === tab.id
                ? 'text-white font-semibold'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.icon}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default DentoTabs; 