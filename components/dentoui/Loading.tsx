interface LoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function Loading({ 
  message = 'Yükleniyor...',
  size = 'md',
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  const textSizeClasses = {
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl'
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-cyan-50">
      <div className="text-center p-8">
        {/* Logo/Brand */}
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg">
          <span className="text-white text-4xl font-bold">🦷</span>
        </div>
        
        {/* Animated loader */}
        <div className="relative mb-6">
          <div className={`mx-auto rounded-full border-4 border-blue-200/50 ${sizeClasses[size]}`}></div>
          <div className={`absolute top-0 left-1/2 transform -translate-x-1/2 animate-spin rounded-full border-4 border-transparent border-t-blue-500 border-r-blue-500 ${sizeClasses[size]}`}></div>
        </div>
        
        {/* Message */}
        <h2 className={`font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600 mb-2 ${textSizeClasses[size]}`}>
          {message}
        </h2>
        
        {/* Subtitle */}
        <p className="text-sm text-gray-500 max-w-sm mx-auto">
          Lütfen bekleyin, verileriniz hazırlanıyor...
        </p>
      </div>
    </div>
  );
}
 