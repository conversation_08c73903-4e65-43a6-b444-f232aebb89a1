import { ButtonHTMLAttributes, FC, ReactNode } from 'react';

interface DentoButtonPrimaryProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  /** Optional icon to show at the left side */
  icon?: ReactNode;
  /** Provide custom Tailwind background/gradient classes. Defaults to the primary blue gradient. */
  bgColor?: string;
}

const DentoButtonPrimary: FC<DentoButtonPrimaryProps> = ({
  icon,
  bgColor,
  className = '',
  children,
  ...props
}) => {
  const backgroundClasses = bgColor ??
    'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700';

  return (
    <button
      {...props}
      className={`w-full ${backgroundClasses} text-white py-3 px-6 rounded-xl font-semibold transform hover:scale-105 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center cursor-pointer ${className}`}
    >
      {icon && <span className="mr-2 flex items-center">{icon}</span>}
      {children}
    </button>
  );
};

export default DentoButtonPrimary; 