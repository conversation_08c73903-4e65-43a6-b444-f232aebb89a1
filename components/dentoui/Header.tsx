'use client';

import Link from 'next/link';
import { ReactNode } from 'react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface HeaderProps {
  title: React.ReactNode;
  description: string;
  breadcrumbs: BreadcrumbItem[];
  rightComponent?: ReactNode;
}

export default function Header({ title, description, breadcrumbs, rightComponent }: HeaderProps) {
  return (
    <div className="bg-gradient-to-r from-blue-50 via-white to-blue-100 border-b border-gray-200">
      <div className="px-8 py-6">
        {/* Breadcrumb Navigation */}
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            {breadcrumbs.map((breadcrumb, index) => (
              <li key={index} className="inline-flex items-center">
                {index > 0 && (
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                )}
                {breadcrumb.href && !breadcrumb.isActive ? (
                  <Link href={breadcrumb.href} className="text-sm font-medium text-gray-500 hover:text-blue-600">
                    {breadcrumb.label}
                  </Link>
                ) : (
                  <span className={`text-sm font-medium ${breadcrumb.isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                    {breadcrumb.label}
                  </span>
                )}
              </li>
            ))}
          </ol>
        </nav>

        {/* Header Title and Action Button */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
            <p className="text-gray-600 text-lg">{description}</p>
          </div>
          
          {rightComponent && (
            <div className="flex items-center space-x-4">
              {rightComponent}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 