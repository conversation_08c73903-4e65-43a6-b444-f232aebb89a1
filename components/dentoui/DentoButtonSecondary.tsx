import { ButtonHTMLAttributes, FC, ReactNode } from 'react';

interface DentoButtonSecondaryProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  /** Optional icon to show at the left side */
  icon?: ReactNode;
  /** Provide custom Tailwind classes for the icon animation on hover */
  iconAnimation?: string;
  /** Provide custom Tailwind background classes. */
  bgColor?: string;
  /** Provide custom Tailwind text color class. Defaults to text-gray-800 */
  textColor?: string;
}

const DentoButtonSecondary: FC<DentoButtonSecondaryProps> = ({
  icon,
  iconAnimation = 'group-hover:rotate-12',
  bgColor = 'bg-white/80 hover:bg-white',
  textColor = 'text-gray-800',
  className = '',
  children,
  ...props
}) => {
  return (
    <button
      {...props}
      className={`group flex items-center space-x-3 px-8 py-4 rounded-xl border border-gray-200 font-semibold transition-all duration-300 hover:shadow-lg cursor-pointer backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 ${bgColor} ${textColor} ${className}`}
    >
      {icon && (
        <span className={`transition-transform duration-300 ${iconAnimation}`}>
          {icon}
        </span>
      )}
      <span>{children}</span>
    </button>
  );
};

export default DentoButtonSecondary; 