'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useUser } from '@/lib/hooks/useUser';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import { 
  Home, 
  Users, 
  FileText, 
  BarChart3, 
  Settings,
  Shield,
  ArrowLeftToLine,
  LogOut
} from 'lucide-react';

export default function Sidebar() {
  const { isCollapsed, isHovered, toggleCollapsed, setIsHovered } = useSidebarStore();
  const [showText, setShowText] = useState(true);
  const pathname = usePathname();
  const router = useRouter();
  const { logout } = useAuth();
  const { isAdmin } = useUser();

  const isExpanded = !isCollapsed || isHovered;

  useEffect(() => {
    if (isExpanded) {
      // Show text with delay after sidebar expands
      const timer = setTimeout(() => {
        setShowText(true);
      }, 100); // Reduced delay for smoother animation
      return () => clearTimeout(timer);
    } else {
      // Hide text with slight delay to allow smooth collapse animation
      const timer = setTimeout(() => {
        setShowText(false);
      }, 50); // Small delay to prevent immediate text hiding
      return () => clearTimeout(timer);
    }
  }, [isExpanded]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const menuItems = [
    { icon: Home, label: 'Panel', href: '/dashboard' },
    { icon: Users, label: 'Hastalar', href: '/dashboard/hastalar' },
    { icon: FileText, label: 'İstem Formu', href: '/dashboard/istem-formu' },
    { icon: BarChart3, label: 'Raporlar', href: '/dashboard/reports' },
    ...(isAdmin ? [{ icon: Shield, label: 'Yönetici', href: '/dashboard/admin' }] : []),
    { icon: Settings, label: 'Ayarlar', href: '/dashboard/settings' },
  ];

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname.startsWith(href);
  };

  return (
    <div 
      className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-50 flex flex-col ${
        isExpanded ? 'w-64' : 'w-18'
      }`}
      style={{
        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-md">
              <span className="text-white font-bold">🦷</span>
            </div>
            {isExpanded && (
              <span 
                className={`ml-3 text-lg font-semibold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent ${
                  showText ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                }`}
                style={{
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              >
                Dento
              </span>
            )}
          </div>
          
          {isExpanded && (
            <button
              onClick={toggleCollapsed}
              className={`p-2 rounded-lg border cursor-pointer hover:scale-105 ${
                isCollapsed 
                  ? 'bg-white hover:bg-blue-50 border-gray-300 hover:border-blue-300' 
                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300'
              } ${
                showText ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
              }`}
              style={{
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <ArrowLeftToLine 
                className={`w-5 h-5 text-blue-600 ${
                  isCollapsed ? 'rotate-180' : ''
                }`}
                style={{
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              />
            </button>
          )}
        </div>
      </div>

      {/* Menu Items */}
      <div className="p-4 flex-1">
        <nav className="space-y-10">
          {menuItems.map((item, index) => {
            const isActive = isActiveRoute(item.href);
            
            return (
              <Link
                key={index}
                href={item.href}
                className={`relative flex items-center text-sm font-medium group ${
                  isExpanded ? 'h-12 px-3' : 'h-10 w-10 justify-center'
                } ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/30 transform scale-105'
                    : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700 hover:scale-105'
                }`}
                style={{
                  borderRadius: isExpanded ? '0.75rem' : '50%',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              >

                
                <div className="flex items-center justify-center min-w-[24px]">
                  <item.icon 
                    className={`w-6 h-6 ${
                      isActive 
                        ? 'text-white' 
                        : 'text-gray-500 group-hover:text-blue-600'
                    }`}
                    style={{
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  />
                </div>
                
                {isExpanded && (
                  <span 
                    className={`ml-4 font-semibold ${
                      showText ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                    } ${
                      isActive ? 'text-white' : 'group-hover:text-blue-700'
                    }`}
                    style={{
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  >
                    {item.label}
                  </span>
                )}
                
                {/* Hover effect background */}
                {!isActive && (
                  <div 
                    className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-hover:opacity-100"
                    style={{
                      borderRadius: isExpanded ? '0.75rem' : '50%',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  ></div>
                )}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Logout Button */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={handleLogout}
          className={`relative flex items-center text-sm font-medium group text-gray-700 hover:bg-red-50 hover:text-red-700 hover:scale-105 cursor-pointer ${
            isExpanded ? 'h-12 px-3 w-full' : 'h-10 w-10 justify-center'
          }`}
          style={{
            borderRadius: isExpanded ? '0.75rem' : '50%',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        >
          <div className="flex items-center justify-center min-w-[24px]">
            <LogOut 
              className="w-6 h-6 text-gray-500 group-hover:text-red-600"
              style={{
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            />
          </div>
          
          {isExpanded && (
            <span 
              className={`ml-4 font-semibold ${
                showText ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
              } group-hover:text-red-700`}
              style={{
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              Çıkış Yap
            </span>
          )}
          
          {/* Hover effect background */}
          <div 
            className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-red-600/10 opacity-0 group-hover:opacity-100"
            style={{
              borderRadius: isExpanded ? '0.75rem' : '50%',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            }}
          ></div>
        </button>
      </div>
    </div>
  );
} 