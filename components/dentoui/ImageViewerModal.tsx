'use client';

import { X, Download, ZoomIn, <PERSON>mOut, RotateCw, ArrowLeft, ArrowRight } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';

interface ImageViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: Array<{
    id: string;
    title: string;
    date: string;
    doctor: string;
    type: string;
    typeColor: string;
    thumbnail: string;
  }>;
  currentImageIndex: number;
  onImageChange?: (index: number) => void;
}

export default function ImageViewerModal({ 
  isOpen, 
  onClose, 
  images, 
  currentImageIndex, 
  onImageChange 
}: ImageViewerModalProps) {
  const [currentIndex, setCurrentIndex] = useState(currentImageIndex);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    setCurrentIndex(currentImageIndex);
  }, [currentImageIndex]);

  useEffect(() => {
    if (onImageChange) {
      onImageChange(currentIndex);
    }
  }, [currentIndex, onImageChange]);

  const handlePrevious = useCallback(() => {
    setCurrentIndex(prev => prev > 0 ? prev - 1 : images.length - 1);
    setZoom(1);
    setRotation(0);
  }, [images.length]);

  const handleNext = useCallback(() => {
    setCurrentIndex(prev => prev < images.length - 1 ? prev + 1 : 0);
    setZoom(1);
    setRotation(0);
  }, [images.length]);

  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  const handleReset = useCallback(() => {
    setZoom(1);
    setRotation(0);
  }, []);

  const handleDownload = () => {
    const currentImage = images[currentIndex];
    if (currentImage) {
      const link = document.createElement('a');
      link.href = currentImage.thumbnail;
      link.download = `${currentImage.title}_${currentImage.date}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          handlePrevious();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        case '+':
        case '=':
          e.preventDefault();
          handleZoomIn();
          break;
        case '-':
          e.preventDefault();
          handleZoomOut();
          break;
        case 'r':
        case 'R':
          e.preventDefault();
          handleRotate();
          break;
        case '0':
          e.preventDefault();
          handleReset();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, handlePrevious, handleNext, handleZoomIn, handleZoomOut, handleRotate, handleReset]);

  if (!isOpen || images.length === 0) return null;

  const currentImage = images[currentIndex];

  return (
    <div className="fixed inset-0 z-50 overflow-hidden animate-fadeIn">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/90 backdrop-blur-sm transition-opacity animate-fadeIn" 
        onClick={onClose} 
      />
      
      {/* Modal Content */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div className="relative w-full h-full max-w-7xl max-h-[95vh] bg-black rounded-2xl overflow-hidden">
          {/* Header */}
          <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 to-transparent p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={onClose}
                  className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
                <div className="text-white">
                  <h2 className="text-lg font-semibold">{currentImage.title}</h2>
                  <p className="text-sm text-gray-300">{currentImage.date} • {currentImage.doctor}</p>
                </div>
              </div>
              
              {/* Image Counter */}
              <div className="text-white text-sm font-medium">
                {currentIndex + 1} / {images.length}
              </div>
            </div>
          </div>

          {/* Image Container */}
          <div className="relative w-full h-full flex items-center justify-center p-20">
            <div 
              className="relative max-w-full max-h-full overflow-hidden"
              style={{
                transform: `scale(${zoom}) rotate(${rotation}deg)`,
                transition: 'transform 0.3s ease-out'
              }}
            >
              <Image
                src={currentImage.thumbnail}
                alt={currentImage.title}
                width={1200}
                height={800}
                className="object-contain max-w-full max-h-full"
                priority
              />
            </div>
          </div>

          {/* Navigation Arrows */}
          {images.length > 1 && (
            <>
              <button
                onClick={handlePrevious}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors cursor-pointer z-20"
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </button>
              <button
                onClick={handleNext}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors cursor-pointer z-20"
              >
                <ArrowRight className="w-6 h-6 text-white" />
              </button>
            </>
          )}

          {/* Controls */}
          <div className="absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black/80 to-transparent p-6">
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={handleZoomOut}
                disabled={zoom <= 0.5}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 disabled:opacity-50 disabled:cursor-not-allowed rounded-xl flex items-center justify-center transition-colors cursor-pointer"
              >
                <ZoomOut className="w-5 h-5 text-white" />
              </button>
              
              <button
                onClick={handleReset}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer"
              >
                <span className="text-white text-sm font-bold">100%</span>
              </button>
              
              <button
                onClick={handleZoomIn}
                disabled={zoom >= 3}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 disabled:opacity-50 disabled:cursor-not-allowed rounded-xl flex items-center justify-center transition-colors cursor-pointer"
              >
                <ZoomIn className="w-5 h-5 text-white" />
              </button>
              
              <button
                onClick={handleRotate}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer"
              >
                <RotateCw className="w-5 h-5 text-white" />
              </button>
              
              <button
                onClick={handleDownload}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer"
              >
                <Download className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Zoom Indicator */}
          <div className="absolute top-20 right-6 z-20">
            <div className="bg-black/60 text-white px-3 py-2 rounded-lg text-sm font-medium">
              {Math.round(zoom * 100)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}