'use client';

import React, { useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { TextAlign } from '@tiptap/extension-text-align';
import { TextStyle } from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  readOnly?: boolean;
  height?: number;
}

const MenuButton: React.FC<{
  onClick: () => void;
  isActive?: boolean;
  children: React.ReactNode;
  title?: string;
}> = ({ onClick, isActive, children, title }) => (
  <button
    onClick={onClick}
    title={title}
    className={`px-2.5 py-1.5 rounded text-sm transition-colors ${
      isActive ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-100'
    }`}
    type="button"
  >
    {children}
  </button>
);

const RichTextEditor: React.FC<RichTextEditorProps> = ({ 
  content, 
  onChange, 
  placeholder = "Rapor içeriğini yazın...",
  className = "",
  readOnly = false,
  height = 300
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3]
        }
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        defaultAlignment: 'left',
      }),
      TextStyle,
      Color,
    ],
    content: content || '',
    editable: !readOnly,
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none min-h-[200px]',
      },
    },
  });

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content || '', { emitUpdate: false });
    }
  }, [content, editor]);

  if (!editor) {
    return (
      <div className={`rounded-xl overflow-hidden ${className}`}>
        <div className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  const headingLevels = [1, 2, 3] as const;

  return (
    <div className={`border border-gray-200 rounded-lg overflow-hidden ${className}`} style={{ minHeight: height }}>
      {!readOnly && (
        <div className="bg-gray-50 border-b border-gray-200 p-2 flex flex-wrap gap-2">
          {/* Text Style Group */}
          <div className="flex gap-1 items-center border-r border-gray-300 pr-2">
            <MenuButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title="Kalın"
            >
              <strong>B</strong>
            </MenuButton>
            <MenuButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title="İtalik"
            >
              <em>I</em>
            </MenuButton>
            <MenuButton
              onClick={() => editor.chain().focus().toggleStrike().run()}
              isActive={editor.isActive('strike')}
              title="Üstü Çizili"
            >
              <s>S</s>
            </MenuButton>
          </div>

          {/* Heading Group */}
          <div className="flex gap-1 items-center border-r border-gray-300 pr-2">
            {headingLevels.map((level) => (
              <MenuButton
                key={level}
                onClick={() => {
                  if (editor.isActive('heading', { level })) {
                    editor.chain().focus().setParagraph().run();
                  } else {
                    editor.chain().focus().toggleHeading({ level }).run();
                  }
                }}
                isActive={editor.isActive('heading', { level })}
                title={`Başlık ${level}`}
              >
                H{level}
              </MenuButton>
            ))}
          </div>

          {/* Alignment Group */}
          <div className="flex gap-1 items-center border-r border-gray-300 pr-2">
            <MenuButton
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              isActive={editor.isActive({ textAlign: 'left' })}
              title="Sola Hizala"
            >
              ←
            </MenuButton>
            <MenuButton
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              isActive={editor.isActive({ textAlign: 'center' })}
              title="Ortala"
            >
              ↔
            </MenuButton>
            <MenuButton
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              isActive={editor.isActive({ textAlign: 'right' })}
              title="Sağa Hizala"
            >
              →
            </MenuButton>
          </div>

          {/* List Group */}
          <div className="flex gap-1 items-center border-r border-gray-300 pr-2">
            <MenuButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              isActive={editor.isActive('bulletList')}
              title="Madde İşaretli Liste"
            >
              • Liste
            </MenuButton>
            <MenuButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              isActive={editor.isActive('orderedList')}
              title="Numaralı Liste"
            >
              1. Liste
            </MenuButton>
          </div>

          {/* Text Color */}
          <div className="flex gap-1 items-center border-r border-gray-300 pr-2">
            <select
              onChange={(e) => editor.chain().focus().setColor(e.target.value).run()}
              className="text-xs border border-gray-300 rounded px-1 py-0.5"
              title="Metin Rengi"
            >
              <option value="#000000">Siyah</option>
              <option value="#dc2626">Kırmızı</option>
              <option value="#2563eb">Mavi</option>
              <option value="#16a34a">Yeşil</option>
              <option value="#ca8a04">Sarı</option>
              <option value="#9333ea">Mor</option>
              <option value="#ea580c">Turuncu</option>
            </select>
          </div>

          {/* Clear Formatting */}
          <div className="flex gap-1 items-center pl-2 ml-auto">
            <MenuButton
              onClick={() => editor.chain().focus().clearNodes().unsetAllMarks().run()}
              title="Formatı Temizle"
            >
              Temizle
            </MenuButton>
          </div>
        </div>
      )}
      
      <div className="p-4 bg-white">
        <EditorContent editor={editor} />
        {!readOnly && !editor?.getText() && (
          <div className="text-gray-400 pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>
    </div>
  );
};

export default RichTextEditor;
