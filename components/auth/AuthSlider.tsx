'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Sparkles, Shield, TrendingUp, Users, Heart, CheckCircle, Zap, Star, Award } from 'lucide-react';

const slides = [
  {
    id: 1,
    title: "Profesyonel",
    subtitle: "Radyoloji Yorumlama",
    description: "<PERSON>ey<PERSON>li uzmanlarımız tarafından yüksek çözünürlüklü dental radyoloji görüntüleriniz titizlikle yorumlanır. Hızlı ve doğru sonuçlarla tedavi planınızı en sağlıklı şekilde oluşturun.",
    icon: Sparkles,
    color: "bg-blue-600",
    glowColor: "shadow-blue-600/30",
    features: [
      { icon: Shield, text: "Gelişmiş Güvenlik", color: "text-blue-300" },
      { icon: TrendingUp, text: "<PERSON><PERSON><PERSON>üme <PERSON>lit<PERSON>ğ<PERSON>", color: "text-blue-400" },
      { icon: Users, text: "<PERSON><PERSON>", color: "text-blue-200" }
    ],
    stats: {
      number: "24/7",
      label: "Uzman Yorumlama",
      change: "<PERSON>sintisiz hizmet"
    }
  },
  {
    id: 2,
    title: "Kapsamlı",
    subtitle: "Raporlama Hizmetleri",
    description: "Radyoloji görüntülerinizin detaylı ve anlaşılır raporları, diş hekimlerinize doğru teşhis koymalarında yardımcı olur.",
    icon: Heart,
    color: "bg-blue-600",
    glowColor: "shadow-blue-600/30",
    features: [
      { icon: CheckCircle, text: "Dijital X-Ray", color: "text-blue-300" },
      { icon: Shield, text: "Veri Koruması", color: "text-blue-400" },
      { icon: Zap, text: "Şimşek Hızı", color: "text-blue-200" }
    ],
    stats: {
      number: "2 saat",
      label: "Ortalama Rapor Süresi",
      change: "Detaylı analiz ile"
    }
  },
  {
    id: 3,
    title: "Uzman",
    subtitle: "Danışmanlık Hizmeti",
    description: "Dental radyoloji konusunda derinlemesine bilgi ve deneyime sahip uzmanlarımız, ihtiyaç duyduğunuz her an size danışmanlık sunar.",
    icon: Award,
    color: "bg-blue-600",
    glowColor: "shadow-blue-600/30",
    features: [
      { icon: Heart, text: "Hasta Bakımı", color: "text-blue-300" },
      { icon: Star, text: "5 Yıldız Değerlendirme", color: "text-blue-400" },
      { icon: Shield, text: "HIPAA Uyumlu", color: "text-blue-200" }
    ],
    stats: {
      number: "15+ yıl",
      label: "Deneyimli Uzmanlar",
      change: "Alanında uzman ekip"
    }
  },
  {
    id: 4,
    title: "Kesintisiz",
    subtitle: "Entegrasyon",
    description: "Modern klinikler için tasarlanmış, iş akışını kesintisiz hale getiren sistemlerle diş görüntüleme süreçlerinizi kolayca yönetin.",
    icon: Zap,
    color: "bg-blue-600",
    glowColor: "shadow-blue-600/30",
    features: [
      { icon: CheckCircle, text: "Kolay Entegrasyon", color: "text-blue-300" },
      { icon: Shield, text: "Güvenli Sistem", color: "text-blue-400" },
      { icon: TrendingUp, text: "Verimlilik", color: "text-blue-200" }
    ],
    stats: {
      number: "100%",
      label: "Sistem Uyumluluğu",
      change: "Tüm PACS sistemleri"
    }
  }
];

export default function AuthSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setDirection(1);
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const [direction, setDirection] = useState(0);

  const nextSlide = () => {
    setDirection(1);
    setCurrentSlide((prev) => (prev + 1) % slides.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setDirection(-1);
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setDirection(index > currentSlide ? 1 : -1);
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0,
    }),
  };

  const transition = {
    x: { type: "spring" as const, stiffness: 300, damping: 30 },
    opacity: { duration: 0.3 }
  };

  const currentSlideData = slides[currentSlide];

  return (
    <div className="relative h-full w-full overflow-hidden">
      {/* Video Background */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover"
      >
        <source src="/videos/video-5.mp4" type="video/mp4" />
      </video>

      
      {/* Content Container */}
      <div className="relative z-10 h-full flex flex-col justify-between p-4 sm:p-6 lg:p-8">
        
        {/* Enhanced Top Section - Logo/Brand */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-600 backdrop-blur-sm rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg shadow-blue-600/30">
            <span className="text-white text-lg sm:text-xl lg:text-2xl">🦷</span>
          </div>
          <div className="text-blue-600 font-bold text-sm sm:text-base lg:text-lg tracking-wide">Diş Görüntüleme</div>
        </div>

        {/* Main Content with Sliding Animation */}
        <div className="flex-1 flex flex-col justify-center relative overflow-hidden px-2 sm:px-4">
          <AnimatePresence mode="wait" custom={direction}>
            <motion.div
              key={currentSlide}
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={transition}
              className="space-y-4 sm:space-y-6 lg:space-y-8 absolute inset-2 sm:inset-4 flex flex-col justify-center"
            >
              
              {/* Enhanced Title Section */}
              <motion.div 
                className="space-y-1 sm:space-y-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-blue-800 leading-tight drop-shadow-2xl">
                  {currentSlideData.title}
                </h1>
                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-blue-800 leading-tight drop-shadow-lg">
                  {currentSlideData.subtitle}
                </h2>
              </motion.div>

              {/* Enhanced Description */}
              <motion.p 
                className="text-blue-800/80 text-sm sm:text-base lg:text-lg leading-relaxed max-w-xs sm:max-w-sm lg:max-w-lg font-medium drop-shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {currentSlideData.description}
              </motion.p>

              {/* Enhanced Stats Card */}
              <motion.div 
                className={`${currentSlideData.color} backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-5 lg:p-6 max-w-xs shadow-2xl ${currentSlideData.glowColor} border border-white/20`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              >
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-1 sm:mb-2 drop-shadow-lg">
                  {currentSlideData.stats.number}
                </div>
                <div className="text-white/95 font-semibold mb-1 text-sm sm:text-base lg:text-lg">
                  {currentSlideData.stats.label}
                </div>
                <div className="text-white/80 text-xs sm:text-sm font-medium">
                  {currentSlideData.stats.change}
                </div>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Enhanced Bottom Section - Navigation */}
        <div className="flex items-center justify-between">
          
          {/* Enhanced Slide Indicators */}
          <div className="flex space-x-2 sm:space-x-3">
            {slides.map((slide, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`h-2 sm:h-3 rounded-full transition-all duration-300 cursor-pointer shadow-lg ${
                  index === currentSlide 
                    ? `w-6 sm:w-8 lg:w-10 ${slide.color} ${slide.glowColor}` 
                    : 'w-2 sm:w-3 bg-black/20 hover:bg-black/30 hover:w-4 sm:hover:w-6'
                }`}
              />
            ))}
          </div>

          {/* Enhanced Navigation Buttons */}
          <div className="flex space-x-2 sm:space-x-3 lg:space-x-4">
            <button
              onClick={prevSlide}
              className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-black/10 backdrop-blur-lg rounded-full border border-blue-500/20 flex items-center justify-center transition-all duration-300 hover:bg-blue-600/30 hover:border-blue-600/40 hover:scale-110 active:scale-95 group cursor-pointer shadow-xl hover:shadow-2xl"
            >
              <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white group-hover:scale-110 group-hover:translate-x-[-1px] transition-all duration-200 drop-shadow-lg" />
            </button>
            <button
              onClick={nextSlide}
              className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-black/10 backdrop-blur-lg rounded-full border border-blue-500/20 flex items-center justify-center transition-all duration-300 hover:bg-blue-600/30 hover:border-blue-600/40 hover:scale-110 active:scale-95 group cursor-pointer shadow-xl hover:shadow-2xl"
            >
              <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white group-hover:scale-110 group-hover:translate-x-[1px] transition-all duration-200 drop-shadow-lg" />
            </button>
          </div>
        </div>
      </div>

     
    </div>
  );
} 