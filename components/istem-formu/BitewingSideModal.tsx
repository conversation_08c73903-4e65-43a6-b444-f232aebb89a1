'use client';

import { useState } from 'react';
import { X, Check } from 'lucide-react';
import FullTeethSet from '../dentoui/FullTeethSet';

interface BitewingSideModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectSides: (sides: string[]) => void;
}

export default function BitewingSideModal({ isOpen, onClose, onSelectSides }: BitewingSideModalProps) {
  const [selectedTeeth, setSelectedTeeth] = useState<string[]>([]);

  if (!isOpen) return null;

  // Define which teeth belong to left and right sides for bitewing
  // Actually for bitewing, we focus on premolars and molars
  const rightSideTeeth = [
    'tooth-18', 'tooth-16', 'tooth-24', 'tooth-26', 'tooth-28',
    'tooth-44', 'tooth-38', 'tooth-36', 'tooth-46', 'tooth-48'
  ];
  
  const leftSideTeeth = [
    'tooth-15', 'tooth-14', 'tooth-17', 'tooth-25', 'tooth-34',
    'tooth-35', 'tooth-47', 'tooth-27', 'tooth-45', 'tooth-37'
  ];

  const allBitewingTeeth = [...leftSideTeeth, ...rightSideTeeth];

  const handleTeethSelect = () => {
    // Individual tooth selection is disabled.
    return;
  };

  const handleConfirmSelection = () => {
    // Determine which sides are selected based on selected teeth
    const hasLeftTeeth = selectedTeeth.some(tooth => leftSideTeeth.includes(tooth));
    const hasRightTeeth = selectedTeeth.some(tooth => rightSideTeeth.includes(tooth));
    
    const selectedSides = [];
    if (hasLeftTeeth) selectedSides.push('left');
    if (hasRightTeeth) selectedSides.push('right');
    
    onSelectSides(selectedSides);
    onClose();
    setSelectedTeeth([]);
  };

  const handleClearSelection = () => {
    setSelectedTeeth([]);
  };

  const handleSelectLeftSide = () => {
    const currentSelection = new Set(selectedTeeth);
    const leftIsSelected = leftSideTeeth.every(tooth => currentSelection.has(tooth));

    if (leftIsSelected) {
      // remove all left teeth
      leftSideTeeth.forEach(tooth => currentSelection.delete(tooth));
    } else {
      // add all left teeth
      leftSideTeeth.forEach(tooth => currentSelection.add(tooth));
    }
    setSelectedTeeth(Array.from(currentSelection));
  };

  const handleSelectRightSide = () => {
    const currentSelection = new Set(selectedTeeth);
    const rightIsSelected = rightSideTeeth.every(tooth => currentSelection.has(tooth));

    if (rightIsSelected) {
      // remove all right teeth
      rightSideTeeth.forEach(tooth => currentSelection.delete(tooth));
    } else {
      // add all right teeth
      rightSideTeeth.forEach(tooth => currentSelection.add(tooth));
    }
    setSelectedTeeth(Array.from(currentSelection));
  };

  const getSelectedSidesText = () => {
    const hasLeftTeeth = selectedTeeth.some(tooth => leftSideTeeth.includes(tooth));
    const hasRightTeeth = selectedTeeth.some(tooth => rightSideTeeth.includes(tooth));
    
    if (hasLeftTeeth && hasRightTeeth) return 'Sol ve Sağ Yarı';
    if (hasLeftTeeth) return 'Sol Yarı';
    if (hasRightTeeth) return 'Sağ Yarı';
    return '';
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={onClose} />
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl transform transition-all animate-slideUp">
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-t-2xl px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-bold text-white">Bitewing Röntgen Diş Seçimi</h2>
                {selectedTeeth.length > 0 && (
                  <span className="bg-white/20 px-3 py-1 rounded-full text-white text-sm font-medium">
                    {selectedTeeth.length} diş seçildi
                  </span>
                )}
              </div>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer hover:scale-110 transform"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>
          
          <div className="p-8">
            <div className="text-center mb-6">
              <p className="text-gray-600 mb-4">
                Bitewing röntgen için aşağıdaki butonları kullanarak taraf seçimi yapın.
              </p>
              
                <div className="bg-gray-50 rounded-xl p-24 mb-6">
                 <FullTeethSet
                   selectedTeeth={selectedTeeth}
                   onTeethSelect={handleTeethSelect}
                   selectableTeeth={allBitewingTeeth}
                   className="transform scale-175"
                 />
               </div>
               
              {/* Quick selection buttons */}
              <div className="relative z-10 flex justify-center space-x-4 mb-6">
                <button
                  onClick={handleSelectLeftSide}
                  className="px-4 py-2 bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-lg transition-colors flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 17l-5-5 5-5m6 10l-5-5 5-5" />
                  </svg>
                  <span>Tüm Sol Tarafı Seç</span>
                </button>
                <button
                  onClick={handleSelectRightSide}
                  className="px-4 py-2 bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-lg transition-colors flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5-5 5M6 7l5 5-5 5" />
                  </svg>
                  <span>Tüm Sağ Tarafı Seç</span>
                </button>
              </div>
              
              <div className="text-sm text-gray-500 mb-4">
                {selectedTeeth.length === 0 
                  ? "Henüz taraf seçilmedi"
                  : `${selectedTeeth.length} diş seçildi${getSelectedSidesText() ? ` - ${getSelectedSidesText()}` : ''}`
                }
              </div>
            </div>

            <div className="flex justify-between space-x-3">
              <button
                onClick={handleClearSelection}
                disabled={selectedTeeth.length === 0}
                className="flex-1 px-6 py-3 border border-gray-300 rounded-xl text-gray-700 font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Seçimi Temizle
              </button>
              
              <button
                onClick={handleConfirmSelection}
                disabled={selectedTeeth.length === 0}
                className="flex-1 px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <Check className="w-5 h-5" />
                <span>Seçimi Onayla</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 