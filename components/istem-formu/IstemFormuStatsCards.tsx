'use client';

interface IstemFormuStatsCardsProps {
  totalFormCount: number;
  pendingFormCount: number;
  completedFormCount: number;
  thisMonthFormCount: number;
}

export default function IstemFormuStatsCards({ 
  totalFormCount, 
  pendingFormCount, 
  completedFormCount, 
  thisMonthFormCount 
}: IstemFormuStatsCardsProps) {

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Forms */}
      <div className="group relative bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-2xl shadow-lg border border-blue-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
            <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+12%</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Toplam Form</h3>
          <p className="text-4xl font-bold text-gray-900">{totalFormCount}</p>
        </div>
      </div>

      {/* Pending Forms */}
      <div className="group relative bg-gradient-to-br from-orange-50 via-white to-orange-50 rounded-2xl shadow-lg border border-orange-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
            <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-orange-600 bg-orange-100/80 px-3 py-1 rounded-full backdrop-blur-sm">{pendingFormCount}</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Bekleyen Form</h3>
          <p className="text-4xl font-bold text-gray-900">{pendingFormCount}</p>
        </div>
      </div>

      {/* Completed Forms */}
      <div className="group relative bg-gradient-to-br from-green-50 via-white to-green-50 rounded-2xl shadow-lg border border-green-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
            <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+5%</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Tamamlanan</h3>
          <p className="text-4xl font-bold text-gray-900">{completedFormCount}</p>
        </div>
      </div>

      {/* This Month */}
      <div className="group relative bg-gradient-to-br from-purple-50 via-white to-purple-50 rounded-2xl shadow-lg border border-purple-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
            <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-purple-600 bg-purple-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+{thisMonthFormCount}</span>
          </div>
        </div>
        <div>
          <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Bu Ay</h3>
          <p className="text-4xl font-bold text-gray-900">{thisMonthFormCount}</p>
        </div>
      </div>
    </div>
  );
} 