/**
 * Set Admin Custom Claims Script
 * 
 * Simple script to grant or revoke admin privileges using Firebase Auth custom claims.
 * 
 * Usage:
 * 1. Install Firebase Admin SDK: npm install firebase-admin
 * 2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable
 * 3. Edit the ADMIN_EMAILS array below with the emails you want to make admin
 * 4. Run: node scripts/set-admin-claims.js
 */

import admin from 'firebase-admin';

// Initialize Firebase Admin (make sure you have credentials set up)
if (!admin.apps.length) {
  admin.initializeApp({
    // Credentials will be loaded from GOOGLE_APPLICATION_CREDENTIALS environment variable
    // or you can specify them here if needed
  });
}

// ============================================================================
// CONFIGURATION: Add the email addresses you want to make admin
// ============================================================================
const ADMIN_EMAILS = [
  // '<EMAIL>',
  // Add more emails here as needed
];

// Set to false to REVOKE admin privileges instead of granting them
const GRANT_ADMIN = true;

// ============================================================================

async function setAdminClaims() {
  console.log('🚀 Setting admin custom claims...\n');
  
  if (ADMIN_EMAILS.length === 0) {
    console.log('❌ No emails specified. Please edit the ADMIN_EMAILS array in this script.');
    return;
  }
  
  const action = GRANT_ADMIN ? 'Granting' : 'Revoking';
  console.log(`${action} admin privileges for ${ADMIN_EMAILS.length} user(s):`);
  ADMIN_EMAILS.forEach(email => console.log(`   - ${email}`));
  console.log('');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const email of ADMIN_EMAILS) {
    try {
      console.log(`🔄 Processing: ${email}`);
      
      // Find the user by email
      const userRecord = await admin.auth().getUserByEmail(email);
      
      // Set the custom claims
      const customClaims = GRANT_ADMIN ? { admin: true } : { admin: false };
      await admin.auth().setCustomUserClaims(userRecord.uid, customClaims);
      
      const status = GRANT_ADMIN ? 'granted' : 'revoked';
      console.log(`✅ Admin privileges ${status} for: ${email} (${userRecord.uid})`);
      successCount++;
      
    } catch (error) {
      console.error(`❌ Failed to process ${email}:`, error.message);
      errorCount++;
    }
  }
  
  console.log('\n📊 Summary:');
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${errorCount}`);
  
  if (successCount > 0) {
    console.log('\n🎉 Admin claims updated successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Users may need to sign out and sign back in for changes to take effect');
    console.log('2. Test admin functionality in your application');
  }
}

async function listAllUsers() {
  console.log('\n🔍 Current users in Firebase Auth:');
  
  try {
    const listUsersResult = await admin.auth().listUsers();
    
    listUsersResult.users.forEach((userRecord) => {
      const isAdmin = userRecord.customClaims?.admin === true;
      const adminStatus = isAdmin ? '👑 ADMIN' : '👤 Regular';
      console.log(`   ${adminStatus} - ${userRecord.email || 'No email'} (${userRecord.uid})`);
    });
    
  } catch (error) {
    console.error('❌ Error listing users:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🔧 Firebase Admin Claims Manager\n');
  
  await setAdminClaims();
  await listAllUsers();
  
  console.log('\n✨ Script completed!');
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Script interrupted. Exiting...');
  process.exit(0);
});

main().catch((error) => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
}); 