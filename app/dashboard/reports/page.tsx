'use client';

import { useState } from 'react';
import Header from '@/components/dentoui/Header';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Loading from '@/components/dentoui/Loading';
import { useAuth } from '@/lib/contexts/AuthContext';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

export default function Reports() {
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [timeRange, setTimeRange] = useState('Son 30 Gün');
  const [yearFilter, setYearFilter] = useState('2024');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('Tüm Durumlar');
  
  // useAuth handles redirecting unauthenticated users to login when options are provided
  const { loading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  if (loading) {
    return <Loading message="Raporlar yükleniyor..." />;
  }

  // Sample report data
  const reports = [
    {
      id: 1,
      patientName: 'Ahmet Yılmaz',
      procedureType: 'Panoramik X-Ray',
      date: '22.11.2024',
      doctor: 'Dr. Sarah Johnson',
      status: 'Tamamlandı',
      statusColor: 'green',
      avatar: 'AY'
    },
    {
      id: 2,
      patientName: 'Ayşe Demir',
      procedureType: 'CBCT',
      date: '21.11.2024',
      doctor: 'Dr. Sarah Johnson',
      status: 'İnceleniyor',
      statusColor: 'yellow',
      avatar: 'AD'
    },
    {
      id: 3,
      patientName: 'Mehmet Kaya',
      procedureType: 'Intraoral',
      date: '20.11.2024',
      doctor: 'Dr. Sarah Johnson',
      status: 'Beklemede',
      statusColor: 'blue',
      avatar: 'MK'
    },
    {
      id: 4,
      patientName: 'Fatma Özkan',
      procedureType: 'Panoramik X-Ray',
      date: '19.11.2024',
      doctor: 'Dr. Sarah Johnson',
      status: 'Tamamlandı',
      statusColor: 'green',
      avatar: 'FÖ'
    }
  ];

  const getStatusBadge = (status: string, color: string) => {
    const colorClasses = {
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      blue: 'bg-blue-100 text-blue-800',
      orange: 'bg-orange-100 text-orange-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color as keyof typeof colorClasses]}`}>
        {status}
      </span>
    );
  };

  // Chart data configurations
  const monthlyPerformanceData = {
    labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'],
    datasets: [
      {
        label: 'Tamamlanan Raporlar',
        data: [145, 162, 178, 189, 201, 225, 198, 234, 251, 267, 289, 312],
        borderColor: 'rgb(147, 51, 234)',
        backgroundColor: 'rgba(147, 51, 234, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(147, 51, 234)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
      {
        label: 'Toplam Hastalar',
        data: [89, 95, 102, 108, 115, 123, 118, 131, 139, 145, 152, 159],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  };

  const imageTypesData = {
    labels: ['Panoramik X-Ray', 'Intraoral X-Ray', 'CBCT', 'Bitewing', 'Diğer'],
    datasets: [
      {
        data: [32, 23, 16, 11, 18],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(147, 51, 234, 0.8)',
          'rgba(251, 146, 60, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(34, 197, 94)',
          'rgb(147, 51, 234)',
          'rgb(251, 146, 60)',
          'rgb(156, 163, 175)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            family: 'system-ui, -apple-system, sans-serif',
          },
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(147, 51, 234, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: 'rgb(107, 114, 128)',
          font: {
            size: 11,
          },
        },
      },
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: 'rgb(107, 114, 128)',
          font: {
            size: 11,
          },
        },
      },
    },
    animation: {
      duration: 2000,
      easing: 'easeInOutQuart' as const,
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 11,
            family: 'system-ui, -apple-system, sans-serif',
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(147, 51, 234, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: { label: string; parsed: number }) {
            return context.label + ': ' + context.parsed + '%';
          }
        }
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
      easing: 'easeInOutQuart' as const,
    },
    cutout: '60%',
  };

  // Breadcrumbs for the header component
  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard' },
    { label: 'Raporlar', isActive: true },
  ];

  return (
    <div className="flex h-screen bg-gray-50">
              <Sidebar />
      
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        {/* Header */}
        <Header
          title="Raporlar ve İstatistikler"
          description="Görüntüleme merkezi performansı ve hasta istatistikleri"
          breadcrumbs={breadcrumbs}
          rightComponent={
            <div className="flex items-center space-x-4">
              {/* Time Range Filter */}
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px] !h-12 border-blue-300 !border-2 !rounded-xl">
                  <SelectValue placeholder="Zaman aralığı seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Son 30 Gün">Son 30 Gün</SelectItem>
                  <SelectItem value="Son 7 Gün">Son 7 Gün</SelectItem>
                  <SelectItem value="Son 3 Ay">Son 3 Ay</SelectItem>
                  <SelectItem value="Son 6 Ay">Son 6 Ay</SelectItem>
                  <SelectItem value="Son 1 Yıl">Son 1 Yıl</SelectItem>
                </SelectContent>
              </Select>

              {/* Download Report Button */}
              <button className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:shadow-lg transform hover:scale-105 cursor-pointer">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span className="font-medium">Rapor İndir</span>
              </button>
            </div>
          }
        />

        {/* Main Content Area */}
        <div className="p-8">
          {/* KPI Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Patients */}
            <div className="group relative bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-2xl shadow-lg border border-blue-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+12% bu ay</span>
                </div>
              </div>
              <div>
                <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Toplam Hasta</h3>
                <p className="text-4xl font-bold text-gray-900">1,247</p>
              </div>
            </div>

            {/* Images Taken */}
            <div className="group relative bg-gradient-to-br from-green-50 via-white to-green-50 rounded-2xl shadow-lg border border-green-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+8% bu ay</span>
                </div>
              </div>
              <div>
                <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Çekilen Görüntü</h3>
                <p className="text-4xl font-bold text-gray-900">3,842</p>
              </div>
            </div>

            {/* Completed Reports */}
            <div className="group relative bg-gradient-to-br from-purple-50 via-white to-purple-50 rounded-2xl shadow-lg border border-purple-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a4 4 0 01-4-4V5a4 4 0 014-4h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a4 4 0 01-4 4z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-semibold text-purple-600 bg-purple-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+15% bu ay</span>
                </div>
              </div>
              <div>
                <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Tamamlanan Rapor</h3>
                <p className="text-4xl font-bold text-gray-900">2,156</p>
              </div>
            </div>

            {/* Monthly Revenue */}
            <div className="group relative bg-gradient-to-br from-orange-50 via-white to-orange-50 rounded-2xl shadow-lg border border-orange-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-semibold text-orange-600 bg-orange-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+23% bu ay</span>
                </div>
              </div>
              <div>
                <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Aylık Gelir</h3>
                <p className="text-4xl font-bold text-gray-900">₺124,500</p>
              </div>
            </div>
          </div>

          {/* Chart Placeholders */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Monthly Performance Chart */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Aylık Performans</h3>
                <Select value={yearFilter} onValueChange={setYearFilter}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Yıl seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2023">2023</SelectItem>
                    <SelectItem value="2022">2022</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="h-64">
                <Line data={monthlyPerformanceData} options={chartOptions} />
              </div>
            </div>

            {/* Distribution of Image Types Chart */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Görüntü Türleri Dağılımı</h3>
              </div>
              <div className="h-64">
                <Doughnut data={imageTypesData} options={doughnutOptions} />
              </div>
            </div>
          </div>

          {/* Data Summary Widgets */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Daily Activity */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Günlük Aktivite</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">Ortalama Günlük Hasta</span>
                  <span className="font-semibold text-gray-900">42</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">Ortalama İşlem Süresi</span>
                  <span className="font-semibold text-gray-900">18 dk</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">Rapor Teslim Süresi</span>
                  <span className="font-semibold text-gray-900">2.4 saat</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">Hasta Memnuniyeti</span>
                  <span className="font-semibold text-green-600">94%</span>
                </div>
              </div>
            </div>

            {/* Most Performed Procedures */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">En Çok Yapılan İşlemler</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 text-sm">Panoramik X-Ray</span>
                  </div>
                  <span className="font-semibold text-gray-900">1,245</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700 text-sm">Intraoral X-Ray</span>
                  </div>
                  <span className="font-semibold text-gray-900">892</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700 text-sm">CBCT</span>
                  </div>
                  <span className="font-semibold text-gray-900">634</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700 text-sm">Bitewing</span>
                  </div>
                  <span className="font-semibold text-gray-900">421</span>
                </div>
              </div>
            </div>

            {/* Recent Reports */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Son Raporlar</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-gray-900">Ahmet Yılmaz</div>
                    <div className="text-xs text-gray-500">Panoramik X-Ray</div>
                  </div>
                  {getStatusBadge('Tamamlandı', 'green')}
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-gray-900">Ayşe Demir</div>
                    <div className="text-xs text-gray-500">CBCT</div>
                  </div>
                  {getStatusBadge('İnceleniyor', 'yellow')}
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-gray-900">Mehmet Kaya</div>
                    <div className="text-xs text-gray-500">Intraoral</div>
                  </div>
                  {getStatusBadge('Beklemede', 'blue')}
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-gray-900">Fatma Özkan</div>
                    <div className="text-xs text-gray-500">Panoramik</div>
                  </div>
                  {getStatusBadge('Tamamlandı', 'green')}
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Report List Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Table Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Detaylı Rapor Listesi</h2>
              <div className="flex items-center space-x-4">
                {/* Search Input */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    className="block w-64 pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Hasta ara..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Durum seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Tüm Durumlar">Tüm Durumlar</SelectItem>
                    <SelectItem value="Tamamlandı">Tamamlandı</SelectItem>
                    <SelectItem value="İnceleniyor">İnceleniyor</SelectItem>
                    <SelectItem value="Beklemede">Beklemede</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HASTA</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İŞLEM TÜRÜ</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TARİH</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DOKTOR</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DURUM</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İŞLEMLER</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reports.map((report) => (
                    <tr key={report.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                              <span className="text-sm font-medium text-white">{report.avatar}</span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{report.patientName}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{report.procedureType}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{report.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{report.doctor}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(report.status, report.statusColor)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button className="text-blue-600 hover:text-blue-900 transition-colors cursor-pointer">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </button>
                          <button className="text-green-600 hover:text-green-900 transition-colors cursor-pointer">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="bg-white px-6 py-3 border-t border-gray-200 flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer">
                  Önceki
                </button>
                <button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer">
                  Sonraki
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Toplam <span className="font-medium">2,156</span> rapordan <span className="font-medium">1</span>-<span className="font-medium">10</span> arası gösteriliyor
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 cursor-pointer">
                      <span className="sr-only">Önceki</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <button className="bg-purple-50 border-purple-500 text-purple-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium cursor-pointer">
                      1
                    </button>
                    <button className="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium cursor-pointer">
                      2
                    </button>
                    <button className="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium cursor-pointer">
                      3
                    </button>
                    <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 cursor-pointer">
                      <span className="sr-only">Sonraki</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 