'use client';

import { useAuth } from '@/lib/contexts/AuthContext';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Loading from '@/components/dentoui/Loading';
import Header from '@/components/dentoui/Header';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

export default function Dashboard() {
  // useAuth returns authentication helpers and handles redirect when options supplied
  const { user, loading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;

  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard', isActive: true },
  ];

  const title = (
    <>
      Tekrar hoş geldiniz,{' '}
      <span className="text-blue-600 font-semibold">
        {user?.email}
      </span>
    </>
  );

  const currentDate = new Date().toLocaleDateString('tr-TR', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  if (loading) {
    return <Loading message="Dashboard yükleniyor..." />;
  }

  // Chart data configurations
  const revenueChartData = {
    labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
    datasets: [
      {
        label: 'Gelir (₺)',
        data: [8500, 9200, 7800, 10500, 11200, 12450],
        borderColor: 'rgb(147, 51, 234)',
        backgroundColor: 'rgba(147, 51, 234, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(147, 51, 234)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  };

  const appointmentsChartData = {
    labels: ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt'],
    datasets: [
      {
        label: 'Randevular',
        data: [12, 19, 15, 25, 22, 18],
        backgroundColor: [
          'rgba(147, 51, 234, 0.8)',
          'rgba(251, 146, 60, 0.8)',
          'rgba(147, 51, 234, 0.8)',
          'rgba(251, 146, 60, 0.8)',
          'rgba(147, 51, 234, 0.8)',
          'rgba(251, 146, 60, 0.8)',
        ],
        borderColor: [
          'rgb(147, 51, 234)',
          'rgb(251, 146, 60)',
          'rgb(147, 51, 234)',
          'rgb(251, 146, 60)',
          'rgb(147, 51, 234)',
          'rgb(251, 146, 60)',
        ],
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      },
    ],
  };

  const treatmentChartData = {
    labels: ['Diş Temizliği', 'Dolgu', 'Kanal Tedavisi', 'Kuron', 'Diş Beyazlatma'],
    datasets: [
      {
        data: [35, 25, 15, 15, 10],
        backgroundColor: [
          'rgba(147, 51, 234, 0.8)',
          'rgba(251, 146, 60, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)',
        ],
        borderColor: [
          'rgb(147, 51, 234)',
          'rgb(251, 146, 60)',
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(236, 72, 153)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            family: 'system-ui, -apple-system, sans-serif',
          },
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(147, 51, 234, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: 'rgb(107, 114, 128)',
          font: {
            size: 11,
          },
        },
      },
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: 'rgb(107, 114, 128)',
          font: {
            size: 11,
          },
        },
      },
    },
    animation: {
      duration: 2000,
      easing: 'easeInOutQuart' as const,
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 11,
            family: 'system-ui, -apple-system, sans-serif',
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(147, 51, 234, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
                 callbacks: {
           label: function(context: { label: string; parsed: number }) {
             return context.label + ': ' + context.parsed + '%';
           }
         }
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
      easing: 'easeInOutQuart' as const,
    },
    cutout: '60%',
  };

  return (
    <div className="flex h-screen bg-gray-50">
              <Sidebar />
      
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header 
          title={title}
          description={`${currentDate}`}
          breadcrumbs={breadcrumbs}
        />
        
        {/* Main Content Area */}
        <div className="p-8">
          {/* Quick Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Patients */}
            <div className="group relative bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-2xl shadow-lg border border-blue-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+12%</span>
                </div>
              </div>
              <div className="flex items-end justify-between">
                <div>
                  <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Toplam Hasta</h3>
                  <p className="text-4xl font-bold text-gray-900">1,247</p>
                </div>
                {/* Mini Bar Chart */}
                <div className="flex items-end space-x-1 h-12">
                  <div className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-sm w-2 transition-all duration-700 ease-out" style={{height: '60%'}}></div>
                  <div className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-sm w-2 transition-all duration-700 ease-out delay-100" style={{height: '80%'}}></div>
                  <div className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-sm w-2 transition-all duration-700 ease-out delay-200" style={{height: '45%'}}></div>
                  <div className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-sm w-2 transition-all duration-700 ease-out delay-300" style={{height: '90%'}}></div>
                  <div className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-sm w-2 transition-all duration-700 ease-out delay-400" style={{height: '75%'}}></div>
                  <div className="bg-gradient-to-t from-blue-600 to-blue-500 rounded-sm w-2 transition-all duration-700 ease-out delay-500" style={{height: '100%'}}></div>
                </div>
              </div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Images Taken */}
            <div className="group relative bg-gradient-to-br from-emerald-50 via-white to-emerald-50 rounded-2xl shadow-lg border border-emerald-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+8%</span>
                </div>
              </div>
              <div className="flex items-end justify-between">
                <div>
                  <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Çekilen Görüntüler</h3>
                  <p className="text-4xl font-bold text-gray-900">3,456</p>
                </div>
                {/* Mini Bar Chart */}
                <div className="flex items-end space-x-1 h-12">
                  <div className="bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-sm w-2 transition-all duration-700 ease-out" style={{height: '70%'}}></div>
                  <div className="bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-sm w-2 transition-all duration-700 ease-out delay-100" style={{height: '55%'}}></div>
                  <div className="bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-sm w-2 transition-all duration-700 ease-out delay-200" style={{height: '85%'}}></div>
                  <div className="bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-sm w-2 transition-all duration-700 ease-out delay-300" style={{height: '65%'}}></div>
                  <div className="bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-sm w-2 transition-all duration-700 ease-out delay-400" style={{height: '95%'}}></div>
                  <div className="bg-gradient-to-t from-emerald-600 to-emerald-500 rounded-sm w-2 transition-all duration-700 ease-out delay-500" style={{height: '100%'}}></div>
                </div>
              </div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-emerald-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Completed Reports */}
            <div className="group relative bg-gradient-to-br from-purple-50 via-white to-purple-50 rounded-2xl shadow-lg border border-purple-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-green-600 bg-green-100/80 px-3 py-1 rounded-full backdrop-blur-sm">+15%</span>
                </div>
              </div>
              <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Tamamlanan Raporlar</h3>
              <p className="text-4xl font-bold text-gray-900 mb-3">892</p>
              <div className="w-full bg-gradient-to-r from-purple-100 to-purple-200 rounded-full h-3 overflow-hidden">
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 h-3 rounded-full shadow-sm transition-all duration-1000 ease-out" style={{width: '67%'}}></div>
              </div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-purple-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Pending Reviews */}
            <div className="group relative bg-gradient-to-br from-orange-50 via-white to-orange-50 rounded-2xl shadow-lg border border-orange-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-orange-600 bg-orange-100/80 px-3 py-1 rounded-full backdrop-blur-sm">23</span>
                </div>
              </div>
              <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">Bekleyen İncelemeler</h3>
              <p className="text-4xl font-bold text-gray-900 mb-3">45</p>
              <div className="w-full bg-gradient-to-r from-orange-100 to-orange-200 rounded-full h-3 overflow-hidden">
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 h-3 rounded-full shadow-sm transition-all duration-1000 ease-out" style={{width: '45%'}}></div>
              </div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-orange-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Revenue Trend Chart */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Gelir Trendi</h2>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Son 6 Ay</span>
                </div>
              </div>
              <div className="h-80">
                <Line data={revenueChartData} options={chartOptions} />
              </div>
            </div>

            {/* Weekly Appointments Chart */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Haftalık Randevular</h2>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Bu Hafta</span>
                </div>
              </div>
              <div className="h-80">
                <Bar data={appointmentsChartData} options={chartOptions} />
              </div>
            </div>
          </div>

          {/* Treatment Distribution & Quick Actions Row */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Treatment Distribution Chart */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Tedavi Dağılımı</h2>
              <div className="h-80">
                <Doughnut data={treatmentChartData} options={doughnutOptions} />
              </div>
            </div>

            {/* Quick Actions */}
            <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Hızlı İşlemler</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Add New Patient */}
                <button className="group relative p-6 bg-gradient-to-br from-purple-500 via-purple-600 to-purple-700 text-white rounded-2xl hover:from-purple-600 hover:via-purple-700 hover:to-purple-800 transition-all duration-300 flex flex-col items-center justify-center space-y-3 transform hover:scale-105 hover:shadow-xl">
                  <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm group-hover:bg-white/30 transition-all duration-300">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span className="font-semibold text-sm text-center">Yeni Hasta Ekle</span>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                {/* Upload Image */}
                <button className="group relative p-6 bg-gradient-to-br from-emerald-500 via-emerald-600 to-emerald-700 text-white rounded-2xl hover:from-emerald-600 hover:via-emerald-700 hover:to-emerald-800 transition-all duration-300 flex flex-col items-center justify-center space-y-3 transform hover:scale-105 hover:shadow-xl">
                  <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm group-hover:bg-white/30 transition-all duration-300">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <span className="font-semibold text-sm text-center">Görüntü Yükle</span>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                {/* View Statistics */}
                <button className="group relative p-6 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white rounded-2xl hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 transition-all duration-300 flex flex-col items-center justify-center space-y-3 transform hover:scale-105 hover:shadow-xl">
                  <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm group-hover:bg-white/30 transition-all duration-300">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <span className="font-semibold text-sm text-center">İstatistikleri Görüntüle</span>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                {/* Schedule Appointment */}
                <button className="group relative p-6 bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 text-white rounded-2xl hover:from-orange-600 hover:via-orange-700 hover:to-orange-800 transition-all duration-300 flex flex-col items-center justify-center space-y-3 transform hover:scale-105 hover:shadow-xl">
                  <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm group-hover:bg-white/30 transition-all duration-300">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="font-semibold text-sm text-center">Randevu Planla</span>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              </div>
            </div>
          </div>

          {/* Recent Activity & Pending Reviews Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-2xl shadow-lg border border-gray-200/50 p-6 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Son Aktiviteler</h2>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-600 font-medium">Canlı güncellemeler</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-purple-50/80 to-white rounded-xl border border-purple-100/50 hover:from-purple-50 hover:shadow-md transition-all duration-300">
                  <div className="relative">
                    <div className="w-11 h-11 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-900 font-semibold truncate">Yeni hasta kaydedildi</p>
                    <p className="text-gray-600 text-sm truncate">Maria Rodriguez kaydını tamamladı</p>
                  </div>
                  <div className="text-right">
                    <span className="text-gray-500 text-xs font-medium">2 dk önce</span>
                    <div className="w-1 h-1 bg-purple-400 rounded-full mt-1 ml-auto"></div>
                  </div>
                </div>
                
                <div className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-orange-50/80 to-white rounded-xl border border-orange-100/50 hover:from-orange-50 hover:shadow-md transition-all duration-300">
                  <div className="relative">
                    <div className="w-11 h-11 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-400 border-2 border-white rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-900 font-semibold truncate">Randevu planlandı</p>
                    <p className="text-gray-600 text-sm truncate">John Smith - Kanal tedavisi</p>
                  </div>
                  <div className="text-right">
                    <span className="text-gray-500 text-xs font-medium">15 dk önce</span>
                    <div className="w-1 h-1 bg-orange-400 rounded-full mt-1 ml-auto"></div>
                  </div>
                </div>
                
                <div className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-green-50/80 to-white rounded-xl border border-green-100/50 hover:from-green-50 hover:shadow-md transition-all duration-300">
                  <div className="relative">
                    <div className="w-11 h-11 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-emerald-400 border-2 border-white rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-900 font-semibold truncate">Tedavi tamamlandı</p>
                    <p className="text-gray-600 text-sm truncate">Sarah Johnson - Diş temizliği</p>
                  </div>
                  <div className="text-right">
                    <span className="text-gray-500 text-xs font-medium">1 saat önce</span>
                    <div className="w-1 h-1 bg-green-400 rounded-full mt-1 ml-auto"></div>
                  </div>
                </div>
                
                <div className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50/80 to-white rounded-xl border border-blue-100/50 hover:from-blue-50 hover:shadow-md transition-all duration-300">
                  <div className="relative">
                    <div className="w-11 h-11 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      </svg>
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 border-2 border-white rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-900 font-semibold truncate">Röntgen yüklendi</p>
                    <p className="text-gray-600 text-sm truncate">David Chen - Azı dişi muayenesi</p>
                  </div>
                  <div className="text-right">
                    <span className="text-gray-500 text-xs font-medium">2 saat önce</span>
                    <div className="w-1 h-1 bg-blue-400 rounded-full mt-1 ml-auto"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Pending Reviews */}
            <div className="bg-gradient-to-br from-white via-orange-50/30 to-white rounded-2xl shadow-lg border border-gray-200/50 p-6 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Bekleyen İncelemeler</h2>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-orange-600 font-semibold bg-orange-100/80 px-3 py-1 rounded-full">45 bekliyor</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="group p-4 bg-gradient-to-r from-red-50/80 to-white rounded-xl border border-red-100/50 hover:from-red-50 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-bold">ER</span>
                      </div>
                      <div>
                        <p className="text-gray-900 font-semibold text-sm">Emily Rodriguez</p>
                        <p className="text-gray-600 text-xs">Çürük Tedavisi</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="bg-red-100 text-red-700 text-xs font-semibold px-2 py-1 rounded-full">Acil</span>
                      <span className="text-gray-500 text-xs">2 gün</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="bg-gray-100 text-gray-700 text-xs font-medium py-2 px-4 rounded-md hover:bg-gray-200 transition-all duration-200">
                      İncele
                    </button>
                  </div>
                </div>

                <div className="group p-4 bg-gradient-to-r from-yellow-50/80 to-white rounded-xl border border-yellow-100/50 hover:from-yellow-50 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-bold">MJ</span>
                      </div>
                      <div>
                        <p className="text-gray-900 font-semibold text-sm">Michael Johnson</p>
                        <p className="text-gray-600 text-xs">Diş Beyazlatma</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="bg-yellow-100 text-yellow-700 text-xs font-semibold px-2 py-1 rounded-full">Orta</span>
                      <span className="text-gray-500 text-xs">1 gün</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="bg-gray-100 text-gray-700 text-xs font-medium py-2 px-4 rounded-md hover:bg-gray-200 transition-all duration-200">
                      İncele
                    </button>
                  </div>
                </div>

                <div className="group p-4 bg-gradient-to-r from-blue-50/80 to-white rounded-xl border border-blue-100/50 hover:from-blue-50 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-bold">AB</span>
                      </div>
                      <div>
                        <p className="text-gray-900 font-semibold text-sm">Anna Brown</p>
                        <p className="text-gray-600 text-xs">Kanal Tedavisi</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="bg-green-100 text-green-700 text-xs font-semibold px-2 py-1 rounded-full">Düşük</span>
                      <span className="text-gray-500 text-xs">3 saat</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="bg-gray-100 text-gray-700 text-xs font-medium py-2 px-4 rounded-md hover:bg-gray-200 transition-all duration-200">
                      İncele
                    </button>
                  </div>
                </div>


              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 