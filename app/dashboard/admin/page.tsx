'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useUser } from '@/lib/hooks/useUser';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Header from '@/components/dentoui/Header';
import Loading from '@/components/dentoui/Loading';
import PendingUsersTable from '@/components/admin/PendingUsersTable';
import AllUsersTable from '@/components/admin/AllUsersTable';
import DentoTabs from '@/components/dentoui/DentoTabs';
import { Users, Clock } from 'lucide-react';

export default function AdminDashboard() {
  useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const { loading: userLoading, isAdmin } = useUser();
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [activeTab, setActiveTab] = useState('pending-users');

  const adminTabs = [
    {
      id: 'pending-users',
      label: 'Bekleyen Kullanıcılar',
      icon: <Clock className="w-5 h-5" />
    },
    {
      id: 'all-users',
      label: 'Tüm Kullanıcılar',
      icon: <Users className="w-5 h-5" />
    }
  ] as const;

  if (userLoading) {
    return <Loading message="Yetkilendirme kontrol ediliyor..." />;
  }

  // Redirect non-admin users
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 text-center max-w-md">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Yetkisiz Erişim</h1>
          <p className="text-gray-600 mb-6">Bu sayfaya erişim yetkiniz bulunmamaktadır.</p>
          <a 
            href="/dashboard" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Ana Sayfaya Dön
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className={`transition-all duration-300 ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="Yönetici Paneli"
          description="Kullanıcıları yönetin ve sistem ayarlarını yapılandırın."
          breadcrumbs={[
            { label: 'Anasayfa', href: '/dashboard' },
            { label: 'Yönetici Paneli', isActive: true }
          ]}
        />

        <div className="p-8">
          <DentoTabs 
            tabs={adminTabs}
            activeTab={activeTab}
            onTabChange={(id) => setActiveTab(id)}
          />

          {activeTab === 'pending-users' && <PendingUsersTable />}
          {activeTab === 'all-users' && <AllUsersTable />}
        </div>
      </main>
    </div>
  );
} 