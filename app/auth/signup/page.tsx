'use client';

import { useState } from "react";
import { Mail, Phone, User, Send, ArrowLeft, CheckCircle } from "lucide-react";
import Link from "next/link";
import AuthSlider from "@/components/auth/AuthSlider";
import { RainbowButton } from "@/components/magicui/rainbow-button";
import { useAuth } from "@/lib/contexts/AuthContext";
import Loading from "@/components/dentoui/Loading";
import { pendingUserService } from "@/lib/services/pendingUserService";
import { pendingUserSchema, type PendingUserInput } from "@/lib/schemas/validation";

export default function ContactUs() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Partial<Record<keyof PendingUserInput, string>>>({});
  
  // useAuth now also handles redirect logic when options are provided
  const { user, loading: authLoading } = useAuth({
    redirectCondition: 'authenticated',
    redirectTo: '/dashboard',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear validation error for this field when user starts typing
    if (validationErrors[name as keyof PendingUserInput]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError(null);
    setValidationErrors({});
    
    try {
      // Validate form data with Zod
      const validatedData = pendingUserSchema.parse(formData);
      
      // Save user to pending users collection
      await pendingUserService.create(validatedData);
      
      setIsSubmitted(true);
      console.log('User successfully added to pending users:', validatedData);
    } catch (error) {
      if (error instanceof Error && 'issues' in error) {
        // Zod validation errors
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        const fieldErrors: Partial<Record<keyof PendingUserInput, string>> = {};
        
        zodError.issues.forEach((issue) => {
          if (issue.path.length > 0) {
            fieldErrors[issue.path[0] as keyof PendingUserInput] = issue.message;
          }
        });
        
        setValidationErrors(fieldErrors);
        setSubmitError('Lütfen form hatalarını düzeltin.');
      } else {
        console.error('Error submitting form:', error);
        setSubmitError('Başvuru gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading while checking auth status
  if (authLoading) {
    return <Loading />;
  }

  // Don't render contact form if user is authenticated
  if (user) {
    return null;
  }

  return (
    <div className="min-h-screen flex bg-white p-8">
      {/* Left side - Visual content */}
      <div className="hidden lg:flex flex-1 relative overflow-hidden rounded-3xl mr-8 max-h-[calc(100vh-4rem)] max-w-[calc(50vw-2rem)]">
        <AuthSlider />
      </div>

      {/* Right side - Contact form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="flex items-center mb-8">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
              <span className="text-white font-bold">🦷</span>
            </div>
            <span className="text-xl font-semibold text-gray-900">Diş Görüntüleme</span>
          </div>

          {/* Back to login link */}
          <Link href="/auth/login" className="inline-flex items-center text-gray-600 hover:text-blue-600 mb-6 transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Giriş sayfasına dön
          </Link>

          {/* Form header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              İletişime Geçin
            </h1>
            <p className="text-gray-600">
              Size nasıl yardımcı olabileceğimizi öğrenin
            </p>
          </div>

          {/* Success message */}
          {isSubmitted ? (
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Başvurunuz Alındı!
                </h2>
                <p className="text-gray-600 mb-4">
                  Başvurunuz başarıyla gönderildi. En kısa sürede sizinle iletişime geçeceğiz.
                </p>
                <p className="text-sm text-gray-500">
                  Başvuru durumunuz hakkında bilgi almak için e-posta adresinizi kontrol edin.
                </p>
              </div>
              <Link 
                href="/auth/login" 
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Giriş Sayfasına Dön
              </Link>
            </div>
          ) : (
            <>
              {/* Error message */}
              {submitError && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-red-700 text-sm">{submitError}</p>
                </div>
              )}

              {/* Contact form */}
              <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                  Ad*
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    value={formData.firstName}
                    onChange={handleChange}
                    className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                      validationErrors.firstName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Adınız"
                  />
                </div>
                {validationErrors.firstName && (
                  <p className="text-red-600 text-sm mt-1">{validationErrors.firstName}</p>
                )}
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                  Soyad*
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="w-5 h-5 text-gray-400" />
                  </div>
                                    <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    value={formData.lastName}
                    onChange={handleChange}
                    className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                      validationErrors.lastName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Soyadınız"
                  />
                </div>
                {validationErrors.lastName && (
                  <p className="text-red-600 text-sm mt-1">{validationErrors.lastName}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                E-posta*
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                    validationErrors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="E-posta adresinizi girin"
                />
              </div>
              {validationErrors.email && (
                <p className="text-red-600 text-sm mt-1">{validationErrors.email}</p>
              )}
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                Telefon Numarası*
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  required
                  value={formData.phone}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                    validationErrors.phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="(*************"
                />
              </div>
              {validationErrors.phone && (
                <p className="text-red-600 text-sm mt-1">{validationErrors.phone}</p>
              )}
            </div>

                <RainbowButton 
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-6 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      İletişime Geç
                    </>
                  )}
                </RainbowButton>
              </form>
            </>
          )}

          {/* Bottom link */}
          {!isSubmitted && (
            <div className="mt-8 text-center">
              <p className="text-gray-600">
                Hesabınız var mı?{' '}
                <Link href="/auth/login" className="text-blue-600 hover:text-blue-700 font-medium transition-colors">
                  Giriş yapın
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 