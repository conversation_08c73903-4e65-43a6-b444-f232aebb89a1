'use client';

import { useState } from "react";
import { Mail, ArrowLeft, Send } from "lucide-react";
import Link from "next/link";
import AuthSlider from "@/components/auth/AuthSlider";
import DentoButtonPrimary from "@/components/dentoui/DentoButtonPrimary";
import { useAuth } from "@/lib/contexts/AuthContext";
import Loading from "@/components/dentoui/Loading";
import { emailService } from "@/lib/services/emailService";

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  // useAuth now also handles redirect logic when options are provided
  const { user, loading: authLoading } = useAuth({
    redirectCondition: 'authenticated',
    redirectTo: '/dashboard',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Validate email format
      if (!emailService.validateEmail(email)) {
        throw new Error('Geçerli bir e-posta adresi girin.');
      }

      // Send password reset email via Brevo
      await emailService.sendPasswordResetEmail(email);
    setIsSubmitted(true);
    } catch (error) {
      console.error('Password reset error:', error);
      setError(error instanceof Error ? error.message : 'Şifre sıfırlama e-postası gönderilirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading while checking auth status
  if (authLoading) {
    return <Loading />;
  }

  // Don't render forgot password form if user is authenticated
  if (user) {
    return null;
  }

  return (
    <div className="min-h-screen flex bg-white p-8">
      {/* Left side - Visual content */}
      <div className="hidden lg:flex flex-1 relative overflow-hidden rounded-3xl mr-8 max-h-[calc(100vh-4rem)] max-w-[calc(50vw-2rem)]">
        <AuthSlider />
      </div>

      {/* Right side - Forgot Password form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="flex items-center mb-8">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
              <span className="text-white font-bold">🦷</span>
            </div>
            <span className="text-xl font-semibold text-gray-900">Diş Görüntüleme</span>
          </div>

          {/* Back to login link */}
          <Link href="/auth/login" className="inline-flex items-center text-gray-600 hover:text-blue-600 mb-6 transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Giriş sayfasına dön
          </Link>

          {!isSubmitted ? (
            <>
              {/* Form header */}
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Şifrenizi mi unuttunuz?
                </h1>
                <p className="text-gray-600">
                  E-posta adresinizi girin, size şifre sıfırlama bağlantısı gönderelim.
                </p>
              </div>

              {/* Error message */}
              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              {/* Forgot Password form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    E-posta*
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="w-5 h-5 text-gray-400" />
                    </div>
                    <input
                      id="email"
                      type="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isLoading}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      placeholder="E-posta adresinizi girin"
                    />
                  </div>
                </div>

                <DentoButtonPrimary
                  type="submit"
                  disabled={isLoading}
                  className="py-4 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  icon={isLoading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                >
                  {isLoading ? 'Gönderiliyor...' : 'Sıfırlama Bağlantısı Gönder'}
                </DentoButtonPrimary>
              </form>
            </>
          ) : (
            <>
              {/* Success message */}
              <div className="mb-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Send className="w-8 h-8 text-blue-600" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2 text-center">
                  E-posta gönderildi!
                </h1>
                <p className="text-gray-600 text-center">
                  <strong>{email}</strong> adresine şifre sıfırlama bağlantısı gönderdik. 
                  E-posta kutunuzu kontrol edin.
                </p>
              </div>

              <div className="space-y-4">
                <p className="text-sm text-gray-500 text-center">
                  E-postayı alamadınız mı? Spam klasörünüzü kontrol edin.
                </p>
                
                <button
                  onClick={() => {
                    setIsSubmitted(false);
                    setError('');
                  }}
                  className="w-full bg-gradient-to-r from-blue-50 to-blue-100 text-blue-800 py-3.5 rounded-xl font-medium hover:from-blue-100 hover:to-blue-200 transition-all duration-200 flex items-center justify-center gap-2 shadow-md hover:shadow-lg transform hover:scale-[1.02] cursor-pointer border border-blue-200"
                >
                  <Mail className="w-5 h-5" />
                  Tekrar Gönder
                </button>
              </div>
            </>
          )}

          {/* Bottom links */}
          <div className="mt-8 text-center">
            <p className="text-gray-600">
              Şifrenizi hatırladınız mı?{' '}
              <Link href="/auth/login" className="text-blue-600 hover:text-blue-700 font-medium transition-colors">
                Giriş yapın
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 