'use client';

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/contexts/AuthContext";
import Loading from "@/components/dentoui/Loading";

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (user) {
        // User is authenticated, redirect to dashboard
        router.push('/dashboard');
      } else {
        // User is not authenticated, redirect to login
        router.push('/auth/login');
      }
    }
  }, [user, loading, router]);

  if (loading) {
    return <Loading message="Yönlendiriliyor..." />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="text-center">
        <div className="w-16 h-16 bg-black rounded-lg flex items-center justify-center mx-auto mb-4">
          <span className="text-white text-2xl font-bold">🦷</span>
        </div>
        <h1 className="text-2xl font-semibold text-black mb-2">Di<PERSON> Görüntüleme</h1>
        <p className="text-gray-600">Yönlendiriliyor...</p>
      </div>
    </div>
  );
}
