import { getFunctions, httpsCallable } from 'firebase/functions';

export const adminService = {
  async setAdminClaim(uid: string, isAdmin: boolean): Promise<void> {
    const functions = getFunctions();
    const setAdminClaim = httpsCallable(functions, 'setAdminClaim');
    
    const result = await setAdminClaim({ uid, isAdmin });
    const response = result.data as { success: boolean; message: string };
    
    if (!response.success) {
      throw new Error('Failed to set admin claim');
    }
  }
}; 