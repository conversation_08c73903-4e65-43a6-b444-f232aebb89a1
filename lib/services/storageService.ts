import { storage } from '../firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

export interface UploadImageResult {
  url: string;
  path: string;
  filename: string;
}

export class StorageService {
  /**
   * Upload a scan image for a specific patient and user
   */
  static async uploadScanImage(
    file: File,
    userId: string,
    patientId: string,
    scanType?: string
  ): Promise<UploadImageResult> {
    try {
      // Generate unique filename with timestamp and scan type
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const scanPrefix = scanType ? `${scanType}_` : '';
      const filename = `${scanPrefix}${timestamp}.${fileExtension}`;
      
      // Create storage reference
      const imagePath = `scan-images/${userId}/${patientId}/${filename}`;
      const imageRef = ref(storage, imagePath);
      
      // Upload file
      const snapshot = await uploadBytes(imageRef, file);
      
      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return {
        url: downloadURL,
        path: imagePath,
        filename: filename
      };
    } catch (error) {
      console.error('Error uploading scan image:', error);
      throw new Error('Failed to upload scan image');
    }
  }

  /**
   * Delete a scan image
   */
  static async deleteScanImage(imagePath: string): Promise<void> {
    try {
      const imageRef = ref(storage, imagePath);
      await deleteObject(imageRef);
    } catch (error) {
      console.error('Error deleting scan image:', error);
      throw new Error('Failed to delete scan image');
    }
  }

  /**
   * Upload a PDF report for a specific patient image
   */
  static async uploadReportPDF(
    file: File,
    userId: string,
    patientId: string,
    imageId: string
  ): Promise<UploadImageResult> {
    try {
      // Generate unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'pdf';
      const filename = `report_${timestamp}.${fileExtension}`;

      // Create storage reference
      const reportPath = `reports/${userId}/${patientId}/${imageId}/${filename}`;
      const reportRef = ref(storage, reportPath);

      // Upload file
      const snapshot = await uploadBytes(reportRef, file);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      return {
        url: downloadURL,
        path: reportPath,
        filename: filename
      };
    } catch (error) {
      console.error('Error uploading report PDF:', error);
      throw new Error('Failed to upload report PDF');
    }
  }

  /**
   * Delete a report PDF
   */
  static async deleteReportPDF(reportPath: string): Promise<void> {
    try {
      const reportRef = ref(storage, reportPath);
      await deleteObject(reportRef);
    } catch (error) {
      console.error('Error deleting report PDF:', error);
      throw new Error('Failed to delete report PDF');
    }
  }

  /**
   * Validate file type and size
   */
  static validateScanFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/tiff'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Sadece JPEG, PNG, WebP ve TIFF dosyaları desteklenir.'
      };
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'Dosya boyutu 10MB\'dan büyük olamaz.'
      };
    }

    return { isValid: true };
  }

  /**
   * Validate PDF file type and size
   */
  static validatePDFFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (file.type !== 'application/pdf') {
      return {
        isValid: false,
        error: 'Sadece PDF dosyaları desteklenir.'
      };
    }

    // Check file size (20MB limit for PDFs)
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'PDF dosya boyutu 20MB\'dan büyük olamaz.'
      };
    }

    return { isValid: true };
  }
}