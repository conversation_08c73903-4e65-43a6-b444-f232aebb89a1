import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
} from 'firebase/firestore';
import { IstemFormuData } from '../types';

export const saveIstemFormu = async (istemFormuData: Omit<IstemFormuData, 'id' | 'createdAt' | 'status'>, userId: string): Promise<string> => {
  try {
    const istemFormuCollection = collection(db, 'users', userId, 'istem-formu');

    const processedData: Omit<IstemFormuData, 'id'> = {
      ...istemFormuData,
      status: 'pending',
      createdAt: new Date(),
    };

    const docRef = await addDoc(istemFormuCollection, processedData);
    return docRef.id;
  } catch (error) {
    console.error('Error saving istem formu: ', error);
    throw new Error('Failed to save istem formu');
  }
};

/**
 * Get waiting istem formu records for a specific patient
 */
export const getWaitingIstemFormuByPatient = async (patientId: string): Promise<IstemFormuData[]> => {
  try {
    const allForms: IstemFormuData[] = [];
    
    // Get all users to check their istem-formu collections
    const usersSnapshot = await getDocs(collection(db, 'users'));
    
    for (const userDoc of usersSnapshot.docs) {
      const istemFormuCollection = collection(db, 'users', userDoc.id, 'istem-formu');
      const waitingQuery = query(
        istemFormuCollection,
        where('patientId', '==', patientId),
        where('status', '==', 'pending'),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(waitingQuery);
      const userForms = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        userId: userDoc.id, // Add the user ID for later use
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as IstemFormuData[];
      
      allForms.push(...userForms);
    }
    
    // Sort by creation date (newest first)
    return allForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  } catch (error) {
    console.error('Error fetching waiting istem formu: ', error);
    throw new Error('Failed to fetch waiting istem formu');
  }
};

/**
 * Get completed istem formu records for a specific patient
 */
export const getCompletedIstemFormuByPatient = async (patientId: string): Promise<IstemFormuData[]> => {
  try {
    const allForms: IstemFormuData[] = [];
    
    const usersSnapshot = await getDocs(collection(db, 'users'));
    
    for (const userDoc of usersSnapshot.docs) {
      const istemFormuCollection = collection(db, 'users', userDoc.id, 'istem-formu');
      const completedQuery = query(
        istemFormuCollection,
        where('patientId', '==', patientId),
        where('status', '==', 'completed'),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(completedQuery);
      const userForms = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        userId: userDoc.id,
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        completedAt: doc.data().completedAt?.toDate() || new Date()
      })) as IstemFormuData[];
      
      allForms.push(...userForms);
    }
    
    return allForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  } catch (error) {
    console.error('Error fetching completed istem formu: ', error);
    throw new Error('Failed to fetch completed istem formu');
  }
};

/**
 * Update istem formu status to completed and add scan images
 */
export const completeIstemFormu = async (
  formId: string, 
  userId: string, 
  scanImages: string[]
): Promise<void> => {
  try {
    const formRef = doc(db, 'users', userId, 'istem-formu', formId);
    
    await updateDoc(formRef, {
      status: 'completed',
      images: scanImages,
      completedAt: new Date()
    });
  } catch (error) {
    console.error('Error completing istem formu: ', error);
    throw new Error('Failed to complete istem formu');
  }
};

/**
 * Delete an istem formu
 */
export const deleteIstemFormu = async (formId: string, userId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'users', userId, 'istem-formu', formId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting istem formu:', error);
    throw new Error('Failed to delete istem formu');
  }
}; 