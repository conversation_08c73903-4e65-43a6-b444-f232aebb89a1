import { httpsCallable } from 'firebase/functions';
import { functions } from '@/lib/firebase';

export interface CustomEmailData {
  to: string;
  toName?: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  templateData?: Record<string, string | number | boolean>;
}

class EmailService {
  private sendPasswordResetEmailFunction = httpsCallable(functions, 'sendPasswordResetEmail');
  private sendCustomEmailFunction = httpsCallable(functions, 'sendCustomEmail');

  /**
   * Send password reset email to user
   */
  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      const result = await this.sendPasswordResetEmailFunction({ email });
      const data = result.data as { success: boolean; message: string };
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to send password reset email');
      }
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
  }

  /**
   * Send custom email (admin only)
   */
  async sendCustomEmail(emailData: CustomEmailData): Promise<void> {
    try {
      const result = await this.sendCustomEmailFunction(emailData);
      const data = result.data as { success: boolean; message: string };
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to send custom email');
      }
    } catch (error) {
      console.error('Error sending custom email:', error);
      throw error;
    }
  }

  /**
   * Get email templates for frontend use
   */
  getEmailTemplates() {
    return {
      passwordReset: {
        subject: 'Şifre Sıfırlama Talebi | Dento',
        preview: 'Şifre sıfırlama bağlantısı içeren e-posta şablonu'
      },
      welcome: {
        subject: 'Dento\'ya Hoş Geldiniz! 🎉',
        preview: 'Yeni kullanıcıları karşılayan hoş geldin e-postası'
      },
      registrationApproval: {
        subject: 'Hesabınız Onaylandı - Kaydınızı Tamamlayın | Dento',
        preview: 'Kullanıcı onaylandığında gönderilen kayıt tamamlama e-postası'
      }
    };
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Create a newsletter signup email template (for future use)
   */
  createNewsletterTemplate(userType: 'dentist' | 'patient' = 'dentist') {
    const baseTemplate = {
      subject: 'Dento Haber Bülteni\'ne Hoş Geldiniz!',
      textContent: `
Merhaba,

Dento haber bültenimize abone olduğunuz için teşekkür ederiz!

Bundan sonra:
- Diş hekimliği alanındaki son gelişmeler
- Platform güncellemeleri ve yeni özellikler
- Uzman görüşleri ve ipuçları
- Özel fırsatlar ve duyurular

hakkında düzenli bilgiler alacaksınız.

İyi günler!
Dento Ekibi
      `
    };

    const dentistTemplate = {
      ...baseTemplate,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>🦷 Dento Haber Bülteni</h2>
          <p>Merhaba,</p>
          <p>Diş hekimliği dünyasından haberdar olmak için doğru yerdesiniz!</p>
          <ul>
            <li>📊 Sektör analiz raporları</li>
            <li>🔬 Yeni tedavi yöntemleri</li>
            <li>💼 İş geliştirme ipuçları</li>
            <li>📱 Platform güncellemeleri</li>
          </ul>
          <p>İyi çalışmalar!</p>
        </div>
      `
    };

    const patientTemplate = {
      ...baseTemplate,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>🦷 Dento Haber Bülteni</h2>
          <p>Merhaba,</p>
          <p>Diş sağlığınız için faydalı bilgiler alacaksınız!</p>
          <ul>
            <li>🦷 Diş bakım ipuçları</li>
            <li>🍎 Beslenme önerileri</li>
            <li>📅 Kontrol hatırlatmaları</li>
            <li>💡 Sağlık tavsiyeleri</li>
          </ul>
          <p>Sağlıklı günler!</p>
        </div>
      `
    };

    return userType === 'dentist' ? dentistTemplate : patientTemplate;
  }
}

export const emailService = new EmailService(); 