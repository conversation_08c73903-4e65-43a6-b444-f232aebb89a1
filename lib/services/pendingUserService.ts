import { db } from '../firebase';
import { collection, addDoc, getDocs, query, orderBy, where, limit, updateDoc, deleteDoc, doc } from 'firebase/firestore';
import { PendingUser } from '../types';
import { convertTimestamps } from '../utils';

export const pendingUserService = {
  async create(pendingUser: Omit<PendingUser, 'id' | 'createdAt' | 'updatedAt' | 'status'>) {
    const docRef = await addDoc(collection(db, 'pendingUsers'), {
      ...pendingUser,
      status: 'pending' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  },

  async getAll(): Promise<PendingUser[]> {
    const q = query(
      collection(db, 'pendingUsers'),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser
    );
  },

  async getByStatus(status: 'pending' | 'approved' | 'rejected'): Promise<PendingUser[]> {
    const q = query(
      collection(db, 'pendingUsers'),
      where('status', '==', status)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser
    );
  },

  async getByEmail(email: string): Promise<PendingUser | null> {
    const q = query(
      collection(db, 'pendingUsers'),
      where('email', '==', email),
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser;
    }
    return null;
  },

  async update(id: string, updates: Partial<PendingUser>) {
    const docRef = doc(db, 'pendingUsers', id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date(),
    });
  },

  async delete(id: string) {
    const docRef = doc(db, 'pendingUsers', id);
    await deleteDoc(docRef);
  },
}; 