'use client';

import { useUserStore } from '@/lib/stores/userStore';
import { userService } from '@/lib/services/userService';
import { User } from '@/lib/types';

export function useUser() {
  const { 
    userProfile, 
    isAdmin, 
    loading, 
    error,
    setUserProfile,
    setError,
  } = useUserStore();

  const updateUserProfile = async (updates: Partial<Omit<User, 'id' | 'createdAt' | 'email'>>) => {
    try {
      if (!userProfile) {
        throw new Error('User profile not found');
      }
      
      setError(null);
      await userService.update(userProfile.id, updates);
      
      const updatedProfile = await userService.getByEmail(userProfile.email);
      setUserProfile(updatedProfile);

    } catch (err) {
      console.error('Update user profile error:', err);
      const errorMessage = 'Failed to update profile';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const refetchUserProfile = async () => {
    if (userProfile?.email) {
      try {
        setError(null);
        const profile = await userService.getByEmail(userProfile.email);
        setUserProfile(profile);
      } catch (err) {
        console.error('Error refetching user profile:', err);
        setError('Failed to refetch user profile');
        setUserProfile(null);
      }
    }
  };

  return {
    userProfile,
    loading,
    error,
    isAdmin,
    updateUserProfile,
    refetchUserProfile,
  };
} 