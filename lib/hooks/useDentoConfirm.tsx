'use client';

import React from 'react';
import { useState, useCallback } from 'react';
import DentoConfirm from '../../components/dentoui/DentoConfirm';

interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'approve' | 'disapprove' | 'delete' | 'warning';
}

interface UseDentoConfirmReturn {
  confirm: (options: ConfirmOptions) => Promise<boolean>;
  ConfirmModal: () => React.ReactNode;
  isLoading: boolean;
}

export function useDentoConfirm(): UseDentoConfirmReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [options, setOptions] = useState<ConfirmOptions>({
    title: '',
    message: ''
  });
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null);

  const confirm = useCallback((confirmOptions: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setOptions(confirmOptions);
      setIsOpen(true);
      setResolvePromise(() => resolve);
    });
  }, []);

  const handleConfirm = useCallback(() => {
    setIsLoading(true);
    // Small delay to show loading state
    setTimeout(() => {
      setIsLoading(false);
      setIsOpen(false);
      if (resolvePromise) {
        resolvePromise(true);
        setResolvePromise(null);
      }
    }, 300);
  }, [resolvePromise]);

  const handleClose = useCallback(() => {
    if (isLoading) return;
    
    setIsOpen(false);
    if (resolvePromise) {
      resolvePromise(false);
      setResolvePromise(null);
    }
  }, [resolvePromise, isLoading]);

  const ConfirmModal = useCallback(() => {
    if (!isOpen) return null;

    return (
      <DentoConfirm
        isOpen={isOpen}
        onClose={handleClose}
        onConfirm={handleConfirm}
        title={options.title}
        message={options.message}
        confirmText={options.confirmText}
        cancelText={options.cancelText}
        type={options.type}
        isLoading={isLoading}
      />
    );
  }, [isOpen, handleClose, handleConfirm, options, isLoading]);

  return {
    confirm,
    ConfirmModal,
    isLoading
  };
}