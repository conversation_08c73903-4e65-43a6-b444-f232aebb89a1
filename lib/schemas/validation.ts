import { z } from 'zod';

// Schema for pending user creation (signup form)
export const pendingUserSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be 50 characters or less')
    .trim(),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be 50 characters or less')
    .trim(),
  email: z.string()
    .email('Invalid email format')
    .max(100, 'Email must be 100 characters or less')
    .toLowerCase()
    .trim(),
  phone: z.string()
    .min(1, 'Phone number is required')
    .max(20, 'Phone number must be 20 characters or less')
    .trim()
});

// Schema for user profile creation
export const userProfileSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be 50 characters or less')
    .trim(),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be 50 characters or less')
    .trim(),
  email: z.string()
    .email('Invalid email format')
    .max(100, 'Email must be 100 characters or less')
    .toLowerCase()
    .trim(),
  phone: z.string()
    .min(1, 'Phone number is required')
    .max(20, 'Phone number must be 20 characters or less')
    .trim(),
  address: z.string()
    .max(200, 'Address must be 200 characters or less')
    .trim()
    .optional()
    .default(''),
  birthDate: z.date().optional().default(() => new Date()),
  role: z.enum(['doctor', 'assistant']).default('doctor'),
  clinic: z.string()
    .max(100, 'Clinic name must be 100 characters or less')
    .trim()
    .optional()
    .default('')
});

// Schema for user profile updates (excludes email and role changes)
export const userProfileUpdateSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be 50 characters or less')
    .trim()
    .optional(),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be 50 characters or less')
    .trim()
    .optional(),
  phone: z.string()
    .min(1, 'Phone number is required')
    .max(20, 'Phone number must be 20 characters or less')
    .trim()
    .optional(),
  address: z.string()
    .max(200, 'Address must be 200 characters or less')
    .trim()
    .optional(),
  birthDate: z.date().optional(),
  clinic: z.string()
    .max(100, 'Clinic name must be 100 characters or less')
    .trim()
    .optional()
});

// Schema for password validation
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must be 128 characters or less')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
    'Password must contain at least one lowercase letter, one uppercase letter, and one number');

// Schema for registration completion
export const registrationCompletionSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// Schema for email validation
export const emailSchema = z.string()
  .email('Invalid email format')
  .max(100, 'Email must be 100 characters or less')
  .toLowerCase()
  .trim();

// Schema for JWT token verification request
export const tokenVerificationSchema = z.object({
  token: z.string().min(1, 'Token is required')
});

// Schema for admin claim setting
export const adminClaimSchema = z.object({
  uid: z.string().min(1, 'User UID is required'),
  isAdmin: z.boolean()
});

// Export types for use throughout the application
export type PendingUserInput = z.infer<typeof pendingUserSchema>;
export type UserProfileInput = z.infer<typeof userProfileSchema>;
export type UserProfileUpdateInput = z.infer<typeof userProfileUpdateSchema>;
export type RegistrationCompletionInput = z.infer<typeof registrationCompletionSchema>;
export type EmailInput = z.infer<typeof emailSchema>;
export type TokenVerificationInput = z.infer<typeof tokenVerificationSchema>;
export type AdminClaimInput = z.infer<typeof adminClaimSchema>; 