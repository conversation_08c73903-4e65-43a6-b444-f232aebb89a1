'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User as FirebaseUser,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendPasswordResetEmail,
  updateProfile,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential,
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/lib/stores/userStore';
import { userService } from '@/lib/services/userService';

interface AuthContextType {
  user: FirebaseUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName?: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Options to automatically redirect based on auth status
export interface UseAuthOptions {
  /**
   * Destination path to navigate to once the redirectCondition is satisfied.
   * Defaults to '/dashboard'.
   */
  redirectTo?: string;
  /**
   * When set to 'authenticated' the redirect will trigger if a user **is** logged in.
   * When set to 'unauthenticated' the redirect will trigger if a user **is not** logged in.
   * Defaults to 'authenticated'.
   */
  redirectCondition?: 'authenticated' | 'unauthenticated';
}

export function useAuth(options?: UseAuthOptions) {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  const router = useRouter();

  // Destructure here to avoid re-creating objects inside the effect below
  const { user, loading } = context;

  // Handle optional redirect logic
  const {
    redirectTo = '/dashboard',
    redirectCondition = 'authenticated',
  } = options || {};

  // Determine if a redirect should occur once auth status is known
  const shouldRedirect =
    !!options && !loading && (
      (redirectCondition === 'authenticated' && !!user) ||
      (redirectCondition === 'unauthenticated' && !user)
    );

  useEffect(() => {
    if (!shouldRedirect) return;

    // Use replace to avoid creating an entry in history stack
    router.replace(redirectTo);
    // Dependency array deliberately omits `options` to avoid endless re-direct loops
  }, [shouldRedirect, router, redirectTo]);

  // While redirecting, mask it as still loading to consumers to avoid UI flash
  return {
    ...context,
    loading: loading || shouldRedirect,
  } as AuthContextType;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const { setUserProfile, setIsAdmin, clearUser, setLoading: setUserStoreLoading } = useUserStore();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        setUser(firebaseUser);
        setUserStoreLoading(true);
        try {
          // Fetch profile and admin claims in parallel
          const [profile, tokenResult] = await Promise.all([
            userService.getById(firebaseUser.uid),
            firebaseUser.getIdTokenResult(),
          ]);

          setUserProfile(profile);
          setIsAdmin(!!tokenResult.claims.admin);
        } catch (error) {
          console.error("Error fetching user data:", error);
          clearUser(); // Clear data on error
        } finally {
          setUserStoreLoading(false);
        }
      } else {
        setUser(null);
        clearUser();
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [setUserProfile, setIsAdmin, clearUser, setUserStoreLoading]);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, displayName?: string) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      if (displayName && result.user) {
        await updateProfile(result.user, { displayName });
      }
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      clearUser(); // Explicitly clear user store on logout
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      if (!user || !user.email) {
        throw new Error('No authenticated user found');
      }

      // Re-authenticate the user with their current password
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Update the password
      await updatePassword(user, newPassword);
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    logout,
    resetPassword,
    changePassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 