import { create } from 'zustand';

interface SidebarState {
  isCollapsed: boolean;
  isHovered: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  setIsHovered: (hovered: boolean) => void;
  toggleCollapsed: () => void;
}

export const useSidebarStore = create<SidebarState>((set) => ({
  isCollapsed: false,
  isHovered: false,
  setIsCollapsed: (collapsed: boolean) => set({ isCollapsed: collapsed }),
  setIsHovered: (hovered: boolean) => set({ isHovered: hovered }),
  toggleCollapsed: () => set((state) => ({ isCollapsed: !state.isCollapsed })),
})); 