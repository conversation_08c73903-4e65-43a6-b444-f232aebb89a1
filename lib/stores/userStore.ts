import { create } from 'zustand';
import { User } from '@/lib/types';

interface UserState {
  userProfile: User | null;
  isAdmin: boolean;
  loading: boolean;
  error: string | null;
  setUserProfile: (userProfile: User | null) => void;
  setIsAdmin: (isAdmin: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearUser: () => void;
}

export const useUserStore = create<UserState>((set) => ({
  userProfile: null,
  isAdmin: false,
  loading: true,
  error: null,
  setUserProfile: (userProfile) => set({ userProfile, loading: false, error: null }),
  setIsAdmin: (isAdmin) => set({ isAdmin }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error, loading: false }),
  clearUser: () => set({ userProfile: null, isAdmin: false, loading: false, error: null }),
})); 