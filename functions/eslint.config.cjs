const globals = require("globals");
const tseslint = require("typescript-eslint");

module.exports = tseslint.config(
  {
    ignores: [
      "lib/", // Ignore the compiled output
      "eslint.config.cjs", // Ignore this config file
      "node_modules/", // Ignore dependencies
      "*.log", // Ignore log files
      ".env*", // Ignore environment files
    ],
  },
  {
    languageOptions: {
      globals: {
        ...globals.node,
      },
      parserOptions: {
        project: "./tsconfig.json",
        sourceType: "module",
      },
    },
  },
  ...tseslint.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  {
    rules: {
      // Code style
      "quotes": ["error", "double"],
      "semi": ["error", "always"],
      "indent": ["error", 2],
      
      // TypeScript specific
      "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/explicit-function-return-type": "warn",
      "@typescript-eslint/no-non-null-assertion": "error",
      "@typescript-eslint/prefer-nullish-coalescing": "error",
      
      // Security rules
      "no-eval": "error",
      "no-implied-eval": "error",
      "no-new-func": "error",
      "no-script-url": "error",
      
      // Node.js specific
      "no-process-exit": "error",
      "no-sync": "warn",
      
      // General best practices
      "no-console": "warn",
      "no-debugger": "error",
      "no-alert": "error",
      "eqeqeq": "error",
      "prefer-const": "error",
      "no-var": "error",
    },
  },
); 