import { onDocumentCreated, onDocumentUpdated, FirestoreEvent, Change } from "firebase-functions/v2/firestore";
import { onCall, CallableRequest } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { DocumentSnapshot } from "firebase-admin/firestore";
import { brevoEmailService } from "./services/brevoService";
import { generateRegistrationUrl, generateDashboardUrl } from "./utils/urlUtils";
import { SecureTokenService, TokenVerificationResult } from "./services/jwtService";

// Initialize Firebase Admin
admin.initializeApp();

// Type definitions for Firebase event data
interface PendingUserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: "pending" | "approved" | "rejected";
}

interface UserData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
}

interface PasswordResetRequest {
  email: string;
}

interface CustomEmailRequest {
  to: string;
  toName?: string;
  templateId: number;
  templateData: Record<string, string | number | boolean>;
}

interface TokenVerificationRequest {
  token: string;
}

interface SetAdminClaimRequest {
  uid: string;
  isAdmin: boolean;
}

interface GenerateRegistrationTokenRequest {
  userData: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
}

export const onPendingUserApprove = onDocumentUpdated({
  document: "pendingUsers/{userId}",
  secrets: [
    "BREVO_API_KEY", 
    "JWT_SECRET", 
    "APP_BASE_URL",
    "BREVO_TEMPLATE_REGISTRATION"
  ],
}, async (event: FirestoreEvent<Change<DocumentSnapshot> | undefined, { userId: string }>) => {
  const newValue = event.data?.after.data() as PendingUserData | undefined;
  const oldValue = event.data?.before.data() as PendingUserData | undefined;

  if (newValue?.status === "approved" && oldValue?.status !== "approved") {
    try {
      const { email, firstName, lastName, phone } = newValue;
      const id = event.data?.after.id;
      
      if (!id) {
        throw new Error("Document ID is missing");
      }
        
      // Create a secure registration link with encoded user data
      const userData = {
        id,
        firstName,
        lastName,
        email,
        phone
      };
      const registrationLink = generateRegistrationUrl(userData);

      // Send registration approval email via Brevo
      await brevoEmailService.sendRegistrationApprovalEmail({
        firstName,
        lastName,
        email,
        phone,
        registrationLink
      });

      logger.info(`Registration approval email sent successfully to ${email}`);
    } catch (error) {
      logger.error("Failed to send registration approval email:", error);
      // Don't throw error to prevent function retry loop
    }
  }
});

// Send welcome email when a new user is created
export const onUserCreate = onDocumentCreated({
  document: "users/{userId}",
  secrets: [
    "BREVO_API_KEY",
    "APP_BASE_URL",
    "BREVO_TEMPLATE_WELCOME"
  ],
}, async (event: FirestoreEvent<DocumentSnapshot | undefined, { userId: string }>) => {
  try {
    const userData = event.data?.data() as UserData | undefined;
    if (!userData) return;

    const { email, firstName } = userData;
    const dashboardLink = generateDashboardUrl();

    // Send welcome email via Brevo
    await brevoEmailService.sendWelcomeEmail({
      email,
      firstName,
      dashboardLink
    });

    logger.info(`Welcome email sent successfully to ${email}`);
  } catch (error) {
    logger.error("Failed to send welcome email:", error);
    // Don't throw error to prevent function retry loop
  }
});

// Cloud function to send password reset email
export const sendPasswordResetEmail = onCall({
  secrets: [
    "BREVO_API_KEY",
    "BREVO_TEMPLATE_PASSWORD_RESET"
  ],
}, async (request: CallableRequest<PasswordResetRequest>): Promise<{success: boolean; message: string}> => {
  try {
    const { email } = request.data;
    
    if (!email) {
      throw new Error("Email is required");
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error("Invalid email format");
    }

    // Check if user exists in Firestore
    const usersRef = admin.firestore().collection("users");
    const userQuery = await usersRef.where("email", "==", email).limit(1).get();
    
    if (userQuery.empty) {
      // For security, don't reveal if user doesn't exist
      logger.info(`Password reset requested for non-existent email: ${email}`);
      return { success: true, message: "If the email exists, a reset link has been sent." };
    }

    // Generate password reset link using Firebase Auth
    const resetLink = await admin.auth().generatePasswordResetLink(email);

    // Send password reset email via Brevo
    await brevoEmailService.sendPasswordResetEmail({
      email,
      resetLink
    });

    logger.info(`Password reset email sent successfully to ${email}`);
    return { success: true, message: "Password reset email sent successfully." };

  } catch (error) {
    logger.error("Failed to send password reset email:", error);
    throw new Error("Failed to send password reset email");
  }
});

// Cloud function to send custom email (for admin use)
export const sendCustomEmail = onCall({
  secrets: ["BREVO_API_KEY"],
}, async (request: CallableRequest<CustomEmailRequest>): Promise<{success: boolean; message: string}> => {
  try {
    // Verify the user is authenticated and has admin role via custom claims
    if (!request.auth) {
      throw new Error("Authentication required");
    }

    // Check admin role via custom claims (more secure than Firestore)
    if (!request.auth.token.admin) {
      throw new Error("Admin privileges required");
    }

    const { 
      to, 
      toName, 
      templateId,
      templateData 
    } = request.data;

    if (!to || !templateId || !templateData) {
      throw new Error("Required fields: to, templateId, templateData");
    }

    // Send custom email via Brevo
    await brevoEmailService.sendTransactionalEmail(
      templateId,
      templateData,
      {
        to,
        toName,
      }
    );

    logger.info(`Custom email sent successfully to ${to} by admin ${request.auth.uid}`);
    return { success: true, message: "Email sent successfully." };

  } catch (error) {
    logger.error("Failed to send custom email:", error);
    throw new Error("Failed to send custom email");
  }
});

// Cloud function to verify JWT tokens
export const verifyRegistrationToken = onCall({
  secrets: ["JWT_SECRET"],
}, (request: CallableRequest<TokenVerificationRequest>): TokenVerificationResult => {
  try {
    const { token } = request.data;
    
    if (!token) {
      return {
        success: false,
        error: "Token is required"
      };
    }

    // Verify the token using our secure service
    const result = SecureTokenService.verifyRegistrationToken(token);
    
    if (result.success) {
      logger.info(`Token verified successfully for user: ${result.payload?.email}`);
    } else {
      logger.warn(`Token verification failed: ${result.error}`);
    }

    return result;

  } catch (error) {
    logger.error("Failed to verify registration token:", error);
    return {
      success: false,
      error: "Token verification failed"
    };
  }
});

// Cloud function to generate registration tokens
export const generateRegistrationToken = onCall({
  secrets: ["JWT_SECRET", "APP_BASE_URL"],
}, (request: CallableRequest<GenerateRegistrationTokenRequest>): {success: boolean; token?: string; error?: string} => {
  try {
    // Verify the user is authenticated and has admin role via custom claims
    if (!request.auth) {
      throw new Error("Authentication required");
    }

    // Check admin role via custom claims (more secure than Firestore)
    if (!request.auth.token.admin) {
      throw new Error("Admin privileges required");
    }

    const { userData: tokenData } = request.data;
    
    if (!tokenData || !tokenData.id || !tokenData.email || !tokenData.firstName || !tokenData.lastName) {
      throw new Error("Invalid user data provided");
    }

    // Generate secure JWT token
    const token = SecureTokenService.generateRegistrationToken(tokenData);

    logger.info(`Registration token generated for user: ${tokenData.email} by admin: ${request.auth.uid}`);
    return { 
      success: true, 
      token 
    };

  } catch (error) {
    logger.error("Failed to generate registration token:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate registration token"
    };
  }
});

// Cloud function to set admin custom claims (only callable by existing admins)
export const setAdminClaim = onCall(async (request: CallableRequest<SetAdminClaimRequest>): Promise<{success: boolean; message: string}> => {
  try {
    // Verify the user is authenticated and has admin role via custom claims
    if (!request.auth) {
      throw new Error("Authentication required");
    }

    // Check admin role via custom claims (only existing admins can create new admins)
    if (!request.auth.token.admin) {
      throw new Error("Admin privileges required");
    }

    const { uid, isAdmin } = request.data;
    
    if (!uid) {
      throw new Error("User UID is required");
    }

    // Set custom claims
    await admin.auth().setCustomUserClaims(uid, { admin: isAdmin });

    logger.info(`Admin claim ${isAdmin ? "granted to" : "revoked from"} user: ${uid} by admin: ${request.auth.uid}`);
    return { 
      success: true, 
      message: `Admin privileges ${isAdmin ? "granted" : "revoked"} successfully` 
    };

  } catch (error) {
    logger.error("Failed to set admin claim:", error);
    throw new Error("Failed to set admin claim");
  }
});


