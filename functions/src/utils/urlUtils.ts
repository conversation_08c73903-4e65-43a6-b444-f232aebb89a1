import { SecureTokenService } from "../services/jwtService";
import * as logger from "firebase-functions/logger";

/**
 * Base URL getter that detects environment
 * @returns The base URL for the current environment
 */
function getBaseUrl(): string {
  // Check if we're in development
  const isDevelopment = process.env.NODE_ENV === "development" || 
                      process.env.FUNCTIONS_EMULATOR === "true";
  
  if (isDevelopment) {
    return "http://localhost:3000";
  }
  
  // Get production URL from environment variables
  const baseUrl = process.env.APP_BASE_URL ?? "https://dento-psi.vercel.app";
  
  if (!process.env.APP_BASE_URL) {
    logger.warn("APP_BASE_URL not set, using default production URL.");
  }
  
  return baseUrl;
}

/**
 * Generate a URL with the base URL
 * @param path - The path to append to the base URL
 * @returns Complete URL
 */
function generateUrl(path: string): string {
  const baseUrl = getBaseUrl();
  // Ensure path starts with /
  const normalizedPath = path.startsWith("/") ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
}

/**
 * Generate dashboard URL
 * @returns Complete dashboard URL
 */
export function generateDashboardUrl(): string {
  return generateUrl("/dashboard");
}

/**
 * Generate registration completion URL with secure JWT token
 * @param userData - User data to encode in the token
 * @returns Complete registration URL with secure token
 */
export function generateRegistrationUrl(userData: {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}): string {
  const secureToken = SecureTokenService.generateRegistrationToken(userData);
  return generateUrl(`/auth/complete-registration?token=${secureToken}`);
} 