import jwt from "jsonwebtoken";
import { logger } from "firebase-functions/v1";

// The secret is loaded from Firebase Functions config at runtime
const getJwtSecret = (): string => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    // This will only fail at runtime if the secret is truly not set
    throw new Error("JWT_SECRET is not configured. Please set it as a secret in your environment.");
  }
  return secret;
};

const JWT_ISSUER = "dento-app";
const DEFAULT_EXPIRY = "48h"; // 48 hours for registration tokens

// Types for JWT payloads
export interface RegistrationTokenPayload {
  type: "registration";
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  iat?: number;
  exp?: number;
  iss?: string;
}

export interface TokenVerificationResult {
  success: boolean;
  payload?: RegistrationTokenPayload;
  error?: string;
}

export class SecureTokenService {
  /**
   * Generate a secure registration token with user data
   * @param userData - User data to encode in the token
   * @param expiresIn - Token expiration time (default: 48h)
   * @returns Signed JWT token
   */
  static generateRegistrationToken(
    userData: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phone: string;
    },
    expiresIn: string = DEFAULT_EXPIRY
  ): string {
    try {
      const payload: RegistrationTokenPayload = {
        type: "registration",
        userId: userData.id,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone,
      };

      const token = jwt.sign(payload, getJwtSecret(), {
        expiresIn,
        issuer: JWT_ISSUER,
        algorithm: "HS256"
      } as jwt.SignOptions);

      logger.info(`Registration token generated for user: ${userData.email}`);
      return token;
    } catch (error) {
      logger.error("Failed to generate registration token:", error);
      throw new Error("Token generation failed");
    }
  }

  /**
   * Verify and decode a registration token
   * @param token - JWT token to verify
   * @returns Token verification result with payload or error
   */
  static verifyRegistrationToken(token: string): TokenVerificationResult {
    try {
      const decoded = jwt.verify(token, getJwtSecret(), {
        issuer: JWT_ISSUER,
        algorithms: ["HS256"]
      } as jwt.VerifyOptions) as RegistrationTokenPayload;

      // Validate token type
      if (decoded.type !== "registration") {
        return {
          success: false,
          error: "Invalid token type"
        };
      }

      // Validate required fields
      if (!decoded.userId || !decoded.email || !decoded.firstName || !decoded.lastName) {
        return {
          success: false,
          error: "Invalid token payload"
        };
      }

      logger.info(`Registration token verified for user: ${decoded.email}`);
      return {
        success: true,
        payload: decoded
      };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return {
          success: false,
          error: "Token has expired"
        };
      } else if (error instanceof jwt.JsonWebTokenError) {
        return {
          success: false,
          error: "Invalid token signature"
        };
      } else {
        logger.error("Token verification failed:", error);
        return {
          success: false,
          error: "Token verification failed"
        };
      }
    }
  }
}

export default SecureTokenService; 