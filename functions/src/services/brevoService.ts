import * as brevo from "@getbrevo/brevo";
import * as logger from "firebase-functions/logger";
import { z } from "zod";

// --- Configuration Fetching ---

// Fetch Brevo API Key
const getBrevoApiKey = (): string => {
  const apiKey = process.env.BREVO_API_KEY;
  if (!apiKey) {
    throw new Error("BREVO_API_KEY is not configured. Please set it in your environment variables or secrets.");
  }
  return apiKey;
};

// Fetch specific Brevo Template ID
const getTemplateId = (templateName: string, envVar: string): number => {
  const templateIdStr = process.env[envVar];
  const templateId = Number(templateIdStr);
  
  if (!templateIdStr || isNaN(templateId) || templateId <= 0) {
    throw new Error(`Brevo template ID for ${templateName} is not properly configured. Please set ${envVar} to a valid positive integer.`);
  }
  
  return templateId;
};

// --- Zod Schemas for Template Parameters ---

const RegistrationParamsSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  phone: z.string(),
  registrationLink: z.string().url(),
});

const PasswordResetParamsSchema = z.object({
  email: z.string().email(),
  resetLink: z.string().url(),
});

const WelcomeParamsSchema = z.object({
  firstName: z.string(),
  dashboardLink: z.string().url(),
  email: z.string().email(),
});

// --- Type Definitions ---

export type RegistrationEmailParams = z.infer<typeof RegistrationParamsSchema>;
export type PasswordResetEmailParams = z.infer<typeof PasswordResetParamsSchema>;
export type WelcomeEmailParams = z.infer<typeof WelcomeParamsSchema>;

export interface SendOptions {
  to: string;
  toName?: string;
  replyTo?: string;
}

// --- Brevo Email Service ---

class BrevoEmailService {
  private apiInstance: brevo.TransactionalEmailsApi | null = null;

  private getApiInstance(): brevo.TransactionalEmailsApi {
    if (!this.apiInstance) {
      this.apiInstance = new brevo.TransactionalEmailsApi();
      this.apiInstance.setApiKey(brevo.TransactionalEmailsApiApiKeys.apiKey, getBrevoApiKey());
    }
    return this.apiInstance;
  }

  async sendTransactionalEmail(
    templateId: number,
    params: Record<string, string | number | boolean>,
    options: SendOptions
  ): Promise<void> {
    try {
      const sendSmtpEmail = new brevo.SendSmtpEmail();
      
      sendSmtpEmail.templateId = templateId;
      sendSmtpEmail.params = params;
      
      sendSmtpEmail.to = [{
        email: options.to,
        name: options.toName ?? options.to
      }];
      
      if (options.replyTo) {
        sendSmtpEmail.replyTo = { email: options.replyTo };
      }

      const api = this.getApiInstance();
      const result = await api.sendTransacEmail(sendSmtpEmail);
      const messageId = result.body?.messageId ? String(result.body.messageId) : "unknown";
      logger.info(`Transactional email (template ${templateId}) sent successfully to ${options.to}`, { messageId });
      
    } catch (error) {
      logger.error(`Failed to send transactional email (template ${templateId}) via Brevo:`, error);
      throw new Error(`Email sending failed: ${String(error)}`);
    }
  }

  async sendRegistrationApprovalEmail(params: RegistrationEmailParams, options?: Omit<SendOptions, "to">): Promise<void> {
    const validatedParams = RegistrationParamsSchema.parse(params);
    const templateId = getTemplateId("registration", "BREVO_TEMPLATE_REGISTRATION");
    await this.sendTransactionalEmail(templateId, validatedParams, { ...options, to: validatedParams.email });
  }

  async sendPasswordResetEmail(params: PasswordResetEmailParams, options?: Omit<SendOptions, "to">): Promise<void> {
    const validatedParams = PasswordResetParamsSchema.parse(params);
    const templateId = getTemplateId("password reset", "BREVO_TEMPLATE_PASSWORD_RESET");
    await this.sendTransactionalEmail(templateId, validatedParams, { ...options, to: validatedParams.email });
  }

  async sendWelcomeEmail(params: WelcomeEmailParams, options?: Omit<SendOptions, "to">): Promise<void> {
    const validatedParams = WelcomeParamsSchema.parse(params);
    const templateId = getTemplateId("welcome", "BREVO_TEMPLATE_WELCOME");
    await this.sendTransactionalEmail(templateId, validatedParams, { ...options, to: validatedParams.email });
  }
}

export const brevoEmailService = new BrevoEmailService();