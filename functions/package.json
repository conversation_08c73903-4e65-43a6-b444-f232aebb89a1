{"name": "functions", "scripts": {"lint": "eslint . --fix", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@getbrevo/brevo": "^2.5.0", "@types/jsonwebtoken": "^9.0.10", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "jsonwebtoken": "^9.0.2", "zod": "^3.25.76"}, "devDependencies": {"eslint": "^8.57.0", "firebase-functions-test": "^3.1.0", "globals": "^15.9.0", "typescript": "^5.7.3", "typescript-eslint": "^7.18.0"}, "private": true}